<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content=" SmartB is Australia’s best resource for the latest sports news, updates, fixtures, form guides and the tools you need to back more winners in any game"
    />
    <!-- HTML Meta Tags -->
    <meta
      name="description"
      content="SmartB is Australia’s best resource for the latest sports news, updates, fixtures, form guides and the tools you need to back more winners in any game"
    />
    <meta name="url" property="og:url" content="" />

    <!-- Google / Search Engine Tags -->

    <meta
      itemprop="name"
      content="SmartB | Smartest Online Sports News and Odds Comparison Platform"
    />
    <meta
      itemprop="description"
      content="SmartB is Australia’s best resource for the latest sports news, updates, fixtures, form guides and the tools you need to back more winners in any game"
    />

    <!-- Facebook Meta Tags -->
    <meta
      property="og:title"
      content="SmartB | Smartest Online Sports News and Odds Comparison Platform"
    />
    <meta property="og:site_name" content="SmartB" />
    <meta
      property="og:description"
      content="SmartB is Australia’s best resource for the latest sports news, updates, fixtures, form guides and the tools you need to back more winners in any game"
    />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="%PUBLIC_URL%/smartb_default.png" />

    <!-- Twitter Meta Tags -->
    <meta
      name="twitter:title"
      content="SmartB | Smartest Online Sports News and Odds Comparison Platform"
    />
    <meta name="twitter:card" content="summary_large_image" />
    <meta property="twitter:site" content="" />
    <meta
      name="twitter:description"
      content="SmartB is Australia’s best resource for the latest sports news, updates, fixtures, form guides and the tools you need to back more winners in any game"
    />
    <meta name="twitter:image" content="%PUBLIC_URL%/smartb_default.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!-- <link rel="canonical" href="https://smartb.com.au/" />
    <meta name="sitemap" content="https://smartb.com.au/sitemap.xml" /> -->
    <title>
      SmartB | Smartest Online Sports News and Odds Comparison Platform
    </title>
    <% if (process.env.REACT_APP_API_BASE_URL == 'https://smartb.com.au/api/') {
    %>

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-PTJ9TC5W");
    </script>
    <!-- End Google Tag Manager -->

    <!-- Google tag (gtag.js) -->
    <script>
      function loadAdsense() {
        var screenWidth =
          window.innerWidth ||
          document.documentElement.clientWidth ||
          document.body.clientWidth;
        var adsenseScript = document.createElement("script");
        adsenseScript.async = true;
        adsenseScript.src =
          "https://www.googletagmanager.com/gtag/js?id=G-X9L120PD0X";
        document.head.appendChild(adsenseScript);
      }
      window.addEventListener("load", loadAdsense);
    </script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-X9L120PD0X");
    </script>
    <% } else if (process.env.REACT_APP_API_BASE_URL ==
    'https://testing.smartb.com.au/api/') { %>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-PTJ9TC5W");
    </script>
    <!-- End Google Tag Manager -->
    <script>
      function loadAdsense() {
        var screenWidth =
          window.innerWidth ||
          document.documentElement.clientWidth ||
          document.body.clientWidth;
        var adsenseScript = document.createElement("script");
        adsenseScript.async = true;
        adsenseScript.src =
          "https://www.googletagmanager.com/gtag/js?id=G-RZ1EFT1M21";
        document.head.appendChild(adsenseScript);
      }
      window.addEventListener("load", loadAdsense);
    </script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-RZ1EFT1M21");
    </script>
    <% } else { %>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-PTJ9TC5W");
    </script>
    <!-- End Google Tag Manager -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-J5BRE44HT7"
    ></script>
    <script id="ad-script-first">
      function loadAdsense() {
        var screenWidth =
          window.innerWidth ||
          document.documentElement.clientWidth ||
          document.body.clientWidth;
        var adsenseScript = document.createElement("script");
        adsenseScript.async = true;
        adsenseScript.src =
          "https://www.googletagmanager.com/gtag/js?id=G-J5BRE44HT7";

        document.head.appendChild(adsenseScript);
      }
      window.addEventListener("load", loadAdsense);
    </script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-J5BRE44HT7");
    </script>
    <script id="ad-script-first-js">
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-J5BRE44HT7");
    </script>
    <% } %>
    <!-- <script id="ad-script-second">
      let countrycode = localStorage.getItem("countrycode");
      if (countrycode === "AU") {
        function secondloadAdsense() {
          var secondscreenWidth =
            window.innerWidth ||
            document.documentElement.clientWidth ||
            document.body.clientWidth;
          if (secondscreenWidth > 768) {
            var secondadsenseScript = document.createElement("script");
            secondadsenseScript.async = true;
            secondadsenseScript.src =
              "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4325140782823061";
            secondadsenseScript.setAttribute("crossorigin", "anonymous");
            document.head.appendChild(secondadsenseScript);
          }
        }
        window.addEventListener("load", secondloadAdsense);
      }
    </script> -->
    <!-- <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4325140782823061"
      crossorigin="anonymous"
    ></script> -->
    <!-- Google tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=UA-249944540-1"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "UA-249944540-1");
    </script>
    <!-- Meta Pixel Code -->
    <script>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod
            ? n.callMethod.apply(n, arguments)
            : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(
        window,
        document,
        "script",
        "https://connect.facebook.net/en_US/fbevents.js"
      );
      fbq("init", "804296624505667");
      fbq("track", "PageView");
    </script>
    <noscript
      ><img
        height="1"
        width="1"
        style="display: none"
        src="https://www.facebook.com/tr?id=804296624505667&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->

    <script>
      !(function (w, d, t) {
        w.TiktokAnalyticsObject = t;
        var ttq = (w[t] = w[t] || []);
        (ttq.methods = [
          "page",
          "track",
          "identify",
          "instances",
          "debug",
          "on",
          "off",
          "once",
          "ready",
          "alias",
          "group",
          "enableCookie",
          "disableCookie",
        ]),
          (ttq.setAndDefer = function (t, e) {
            t[e] = function () {
              t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
            };
          });
        for (var i = 0; i < ttq.methods.length; i++)
          ttq.setAndDefer(ttq, ttq.methods[i]);
        (ttq.instance = function (t) {
          for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++)
            ttq.setAndDefer(e, ttq.methods[n]);
          return e;
        }),
          (ttq.load = function (e, n) {
            var i = "https://analytics.tiktok.com/i18n/pixel/events.js";
            (ttq._i = ttq._i || {}),
              (ttq._i[e] = []),
              (ttq._i[e]._u = i),
              (ttq._t = ttq._t || {}),
              (ttq._t[e] = +new Date()),
              (ttq._o = ttq._o || {}),
              (ttq._o[e] = n || {});
            n = document.createElement("script");
            (n.type = "text/javascript"),
              (n.async = !0),
              (n.src = i + "?sdkid=" + e + "&lib=" + t);
            e = document.getElementsByTagName("script")[0];
            e.parentNode.insertBefore(n, e);
          });

        ttq.load("CK8FE0BC77U25LTG2BL0");
        ttq.page();
      })(window, document, "ttq");
    </script>
    <!-- Reddit Pixel -->
    <script>
      !(function (w, d) {
        if (!w.rdt) {
          var p = (w.rdt = function () {
            p.sendEvent
              ? p.sendEvent.apply(p, arguments)
              : p.callQueue.push(arguments);
          });
          p.callQueue = [];
          var t = d.createElement("script");
          (t.src = "https://www.redditstatic.com/ads/pixel.js"), (t.async = !0);
          var s = d.getElementsByTagName("script")[0];
          s.parentNode.insertBefore(t, s);
        }
      })(window, document);
      rdt("init", "a2_dgcmda6bg530", {
        optOut: false,
        useDecimalCurrencyValues: true,
        aaid: "<AAID-HERE>",
        email: "<EMAIL-HERE>",
        externalId: "<EXTERNAL-ID-HERE>",
        idfa: "<IDFA-HERE>",
      });
      rdt("track", "PageVisit");
    </script>
    <!-- DO NOT MODIFY UNLESS TO REPLACE A USER IDENTIFIER -->
    <!-- End Reddit Pixel -->
    <!-- Snap Pixel Code -->
    <script type="text/javascript">
      (function (e, t, n) {
        if (e.snaptr) return;
        var a = (e.snaptr = function () {
          a.handleRequest
            ? a.handleRequest.apply(a, arguments)
            : a.queue.push(arguments);
        });
        a.queue = [];
        var s = "script";
        r = t.createElement(s);
        r.async = !0;
        r.src = n;
        var u = t.getElementsByTagName(s)[0];
        u.parentNode.insertBefore(r, u);
      })(window, document, "https://sc-static.net/scevent.min.js");

      snaptr("init", "6e7a88b3-4428-4a48-891f-a1e5263de0d0", {
        user_email: "__INSERT_USER_EMAIL__",
      });

      snaptr("track", "PAGE_VIEW");
    </script>
    <!-- End Snap Pixel Code -->
    <script>
      document
        .querySelector("meta[property='og:url']")
        .setAttribute("content", location.href);
      document
        .querySelector("meta[property='twitter:site']")
        .setAttribute("content", location.href);
    </script>
  </head>

  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-PTJ9TC5W"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div id="root"></div>
  </body>
</html>
