<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="sf_pro_textregular" horiz-adv-x="1328" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="578" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="578" />
<glyph unicode="&#x09;" horiz-adv-x="578" />
<glyph unicode="&#xa0;" horiz-adv-x="578" />
<glyph unicode="!" horiz-adv-x="637" d="M180 121q0 61 39 100.5t100 39.5t99.5 -39t38.5 -101q0 -61 -38.5 -100t-99.5 -39t-100 39t-39 100zM218 1444h200l-17 -964h-166z" />
<glyph unicode="&#x22;" horiz-adv-x="895" d="M179 904v539h160v-539h-160zM556 904v539h160v-539h-160z" />
<glyph unicode="#" horiz-adv-x="1296" d="M40 403l28 144h237l73 364h-247l29 143h247l81 389h142l-78 -389h326l81 389h143l-80 -389h249l-28 -143h-251l-75 -364h261l-28 -144h-262l-82 -403h-143l82 403h-328l-82 -403h-142l81 403h-234zM445 544h330l75 369h-330z" />
<glyph unicode="$" d="M150 395h171q8 -100 81 -164t196 -79v520l-80 22q-161 43 -247 139t-86 234q0 163 111 268.5t302 124.5v140h134v-139q186 -16 300.5 -122.5t119.5 -269.5h-170q-3 94 -71.5 159t-178.5 78v-489l86 -21q185 -49 272.5 -143.5t87.5 -244.5q0 -178 -117 -285.5t-329 -124.5 v-135h-134v134q-203 15 -325 123.5t-123 274.5zM357 1077q0 -89 61.5 -147.5t179.5 -83.5v460q-105 -6 -173 -70.5t-68 -158.5zM732 149q130 7 201.5 71t71.5 174q0 100 -66 160.5t-207 90.5v-496z" />
<glyph unicode="%" horiz-adv-x="1610" d="M80 1121q0 152 86.5 249.5t221.5 97.5t221 -97t86 -250t-86 -250.5t-221 -97.5t-221.5 97.5t-86.5 250.5zM188 0l551 752l506 691h182l-560 -766l-495 -677h-184zM240 1121q0 -100 40.5 -160t107.5 -60q66 0 106.5 60.5t40.5 159.5q0 101 -39.5 160t-107.5 59 q-67 0 -107.5 -59.5t-40.5 -159.5zM915 323q0 152 86.5 249.5t221.5 97.5t221 -97t86 -250t-86 -250.5t-221 -97.5t-221.5 97.5t-86.5 250.5zM1075 323q0 -100 40.5 -160t107.5 -60q66 0 106.5 60.5t40.5 159.5q0 101 -39.5 160t-107.5 59q-67 0 -107.5 -59.5t-40.5 -159.5z " />
<glyph unicode="&#x26;" horiz-adv-x="1457" d="M115 385q0 252 286 403l16 8t22.5 11t17.5 9q-99 104 -140 181.5t-41 156.5q0 139 105 231t263 92q160 0 264 -91.5t104 -233.5q0 -111 -72.5 -198t-239.5 -175l361 -388q68 136 68 388v25h168v-26q0 -325 -119 -509l251 -269h-226l-133 144q-93 -81 -214.5 -126 t-252.5 -45q-215 0 -351.5 115t-136.5 297zM295 397q0 -122 89.5 -197t234.5 -75q99 0 192.5 36.5t152.5 98.5l-408 446q-11 -4 -51 -24q-210 -109 -210 -285zM448 1144q0 -60 35 -119t120 -144q132 67 186 127t54 138q0 84 -55 136t-143 52q-86 0 -141.5 -53t-55.5 -137z " />
<glyph unicode="'" horiz-adv-x="518" d="M179 904v539h160v-539h-160z" />
<glyph unicode="(" horiz-adv-x="782" d="M242 600q0 530 284 906h146q-107 -138 -180 -396t-73 -510q0 -253 72.5 -511.5t180.5 -395.5h-146q-284 376 -284 907z" />
<glyph unicode=")" horiz-adv-x="782" d="M110 -307q108 137 180.5 395.5t72.5 511.5q0 252 -73 510t-180 396h146q284 -376 284 -906q0 -531 -284 -907h-146z" />
<glyph unicode="*" horiz-adv-x="1032" d="M153 931l260 131l-260 132l67 118l244 -163l-16 294h136l-16 -294l244 163l67 -118l-259 -132l259 -131l-67 -116l-244 158l16 -289h-136l16 290l-244 -159z" />
<glyph unicode="+" d="M155 506v148h435v435h148v-435h435v-148h-435v-435h-148v435h-435z" />
<glyph unicode="," horiz-adv-x="608" d="M142 -367l95 569h191l-157 -569h-129z" />
<glyph unicode="-" horiz-adv-x="966" d="M155 499v163h656v-163h-656z" />
<glyph unicode="." horiz-adv-x="608" d="M174 120q0 54 38.5 92t91.5 38q54 0 92 -38t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5z" />
<glyph unicode="/" horiz-adv-x="682" d="M0 -307l530 1813h152l-530 -1813h-152z" />
<glyph unicode="0" horiz-adv-x="1290" d="M134 722q0 357 135 556t377 199q241 0 375.5 -200t134.5 -558q0 -359 -133.5 -556t-376.5 -197q-244 0 -378 198t-134 558zM315 722q0 -289 85.5 -444t245.5 -155t244.5 154.5t84.5 444.5q0 287 -85.5 442t-243.5 155t-244.5 -155.5t-86.5 -441.5z" />
<glyph unicode="1" horiz-adv-x="955" d="M123 977v190l384 276h179v-1443h-180v1243h-16z" />
<glyph unicode="2" horiz-adv-x="1237" d="M152 1036v1q0 192 130.5 316t331.5 124q194 0 325.5 -116.5t131.5 -288.5q0 -113 -64 -222t-241 -299l-347 -373v-16h676v-162h-934v124l499 545q139 151 185 230t46 166q0 108 -81.5 181.5t-200.5 73.5q-127 0 -205 -78t-78 -205v-1h-174z" />
<glyph unicode="3" horiz-adv-x="1284" d="M140 376h174q12 -115 102 -184t230 -69q142 0 233.5 76t91.5 195q0 125 -89 198t-243 73h-183v154h175q123 0 201.5 72t78.5 184q0 110 -76 177.5t-201 67.5t-202 -65.5t-88 -181.5h-175q14 188 140 296t332 108q124 0 228.5 -50.5t164.5 -137.5t60 -190q0 -129 -67 -218 t-186 -118v-16q147 -19 231 -114.5t84 -243.5q0 -178 -147 -300.5t-361 -122.5q-220 0 -359 112.5t-149 297.5z" />
<glyph unicode="4" horiz-adv-x="1318" d="M117 299v164q198 352 618 980h261v-983h205v-161h-205v-299h-176v299h-703zM301 459h520v817h-12q-234 -340 -508 -805v-12z" />
<glyph unicode="5" horiz-adv-x="1267" d="M152 384h177q12 -117 97.5 -188.5t212.5 -71.5q145 0 233.5 91t88.5 239t-88.5 240.5t-230.5 92.5q-98 0 -176 -42.5t-122 -119.5h-169l79 818h805v-161h-664l-41 -463h16q105 123 305 123q206 0 336 -135t130 -348q0 -220 -140 -356.5t-364 -136.5q-205 0 -339 115.5 t-146 302.5z" />
<glyph unicode="6" horiz-adv-x="1305" d="M128 697q0 372 145 576t410 204q184 0 311.5 -101.5t161.5 -275.5h-185q-29 101 -106.5 159t-183.5 58q-176 0 -273.5 -152t-104.5 -440h16q53 105 154 163.5t230 58.5q206 0 340 -136.5t134 -346.5q0 -218 -142 -357.5t-364 -139.5q-163 0 -284 84.5t-189 243.5 q-70 164 -70 402zM343 464q0 -145 93.5 -242t232.5 -97t232.5 95.5t93.5 237.5q0 148 -90.5 240.5t-233.5 92.5t-235.5 -92t-92.5 -235z" />
<glyph unicode="7" horiz-adv-x="1167" d="M97 1282v161h955v-167l-642 -1276h-189l648 1268v14h-772z" />
<glyph unicode="8" horiz-adv-x="1308" d="M134 386q0 141 82 242t218 126v16q-110 30 -175.5 117t-65.5 205q0 169 129.5 277t331.5 108t331.5 -108t129.5 -277q0 -119 -64.5 -205t-176.5 -117v-16q136 -25 218 -126t82 -242q0 -185 -145.5 -302.5t-374.5 -117.5t-374.5 117.5t-145.5 302.5zM316 399 q0 -124 94 -201.5t244 -77.5t244 77.5t94 201.5t-94 201.5t-244 77.5t-244 -77.5t-94 -201.5zM370 1075q0 -110 78 -177.5t206 -67.5t206 67.5t78 177.5q0 112 -78 180t-206 68t-206 -68t-78 -180z" />
<glyph unicode="9" horiz-adv-x="1305" d="M128 980q0 218 142 357.5t364 139.5q162 0 284 -84t189 -244q70 -164 70 -402q0 -372 -145 -576t-410 -204q-184 0 -311.5 101.5t-161.5 275.5h185q29 -101 106.5 -159t183.5 -58q176 0 273.5 152t104.5 440h-8h-4h-4q-53 -105 -154 -163.5t-230 -58.5q-206 0 -340 136.5 t-134 346.5zM310 986q0 -148 90.5 -240.5t233.5 -92.5t235.5 92t92.5 235q0 145 -93.5 242t-232.5 97t-232.5 -95.5t-93.5 -237.5z" />
<glyph unicode=":" horiz-adv-x="608" d="M174 120q0 54 38.5 92t91.5 38q54 0 92 -38t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5zM174 889q0 54 38.5 92t91.5 38q54 0 92 -38t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5z" />
<glyph unicode=";" horiz-adv-x="608" d="M117 -367l95 569h191l-157 -569h-129zM174 889q0 54 38.5 92t91.5 38q54 0 92 -38t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5z" />
<glyph unicode="&#x3c;" d="M224 526v142l880 509v-186l-708 -386v-16l708 -386v-186z" />
<glyph unicode="=" d="M160 312v149h1008v-149h-1008zM160 700v149h1008v-149h-1008z" />
<glyph unicode="&#x3e;" d="M224 17v186l708 386v16l-708 386v186l880 -509v-142z" />
<glyph unicode="?" horiz-adv-x="1050" d="M80 1066q9 171 121.5 279.5t314.5 108.5q187 0 305.5 -103t118.5 -259q0 -219 -209 -345q-94 -56 -128.5 -106t-34.5 -130v-65h-175v92q-1 98 47.5 170t152.5 134q88 54 124.5 108t36.5 133q0 94 -67.5 154.5t-177.5 60.5q-113 0 -181.5 -62t-76.5 -170h-171zM355 121 q0 61 39 100.5t100 39.5t99.5 -39t38.5 -101q0 -61 -38.5 -100t-99.5 -39t-100 39t-39 100z" />
<glyph unicode="@" horiz-adv-x="1880" d="M143 601q0 183 60.5 338.5t168.5 264.5t261.5 170t335.5 61q234 0 416.5 -93t284 -260.5t101.5 -382.5q0 -245 -102 -389t-274 -144q-88 0 -149.5 47t-70.5 121h-16q-32 -76 -97 -117.5t-153 -41.5q-156 0 -252 117t-96 308q0 182 97 297.5t251 115.5q79 0 142 -38 t92 -103h16v122h151v-587q0 -53 29.5 -82t83.5 -29q94 0 150.5 110t56.5 295q0 179 -84.5 317t-238 214.5t-351.5 76.5q-193 0 -346.5 -91t-239.5 -254.5t-86 -368.5q0 -320 191.5 -511t512.5 -191q103 0 195.5 13t139.5 35v-126q-142 -49 -337 -49q-387 0 -615 226 t-228 609zM721 596q0 -130 56.5 -205t154.5 -75q103 0 164 76t61 204t-60.5 203.5t-162.5 75.5q-100 0 -156.5 -74t-56.5 -205z" />
<glyph unicode="A" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM456 566h468l-226 644h-16z" />
<glyph unicode="B" horiz-adv-x="1346" d="M184 0v1443h552q198 0 312.5 -97t114.5 -263q0 -112 -72.5 -202t-176.5 -109v-16q148 -19 235 -113.5t87 -237.5q0 -191 -129.5 -298t-359.5 -107h-563zM364 159h356q163 0 247 65t84 190q0 252 -348 252h-339v-507zM364 821h288q329 0 329 231q0 110 -74.5 171 t-208.5 61h-334v-463z" />
<glyph unicode="C" horiz-adv-x="1466" d="M128 721q0 349 171 552.5t465 203.5q228 0 390 -130t194 -339h-183q-36 140 -144 221.5t-257 81.5q-208 0 -329.5 -159t-121.5 -431t122 -430.5t330 -158.5q151 0 258.5 72.5t141.5 197.5h183q-36 -199 -194.5 -317.5t-388.5 -118.5q-294 0 -465.5 203t-171.5 552z" />
<glyph unicode="D" horiz-adv-x="1487" d="M184 0v1443h495q321 0 501 -190.5t180 -530.5q0 -341 -179.5 -531.5t-501.5 -190.5h-495zM364 162h303q241 0 375 147t134 411q0 265 -134.5 413t-374.5 148h-303v-1119z" />
<glyph unicode="E" horiz-adv-x="1220" d="M184 0v1443h894v-162h-714v-463h677v-160h-677v-496h714v-162h-894z" />
<glyph unicode="F" horiz-adv-x="1172" d="M184 0v1443h876v-162h-696v-496h639v-160h-639v-625h-180z" />
<glyph unicode="G" horiz-adv-x="1529" d="M128 722q0 342 179 548.5t475 206.5q240 0 402.5 -125t201.5 -339h-183q-47 146 -154 222t-267 76q-214 0 -341.5 -160t-127.5 -429q0 -273 126.5 -431.5t343.5 -158.5q194 0 313.5 113t119.5 296v35h-406v158h585v-176q0 -267 -168 -429.5t-445 -162.5 q-299 0 -476.5 205t-177.5 551z" />
<glyph unicode="H" horiz-adv-x="1520" d="M184 0v1443h180v-624h792v624h180v-1443h-180v657h-792v-657h-180z" />
<glyph unicode="I" horiz-adv-x="548" d="M184 0v1443h180v-1443h-180z" />
<glyph unicode="J" horiz-adv-x="1102" d="M86 337h179q3 -91 67 -148t165 -57q116 0 178.5 69.5t62.5 201.5v1040h180v-1041q0 -206 -112 -321t-308 -115q-182 0 -297 103t-115 268z" />
<glyph unicode="K" horiz-adv-x="1349" d="M184 0v1443h180v-701h16l632 701h226l-577 -623l630 -820h-233l-521 695l-173 -191v-504h-180z" />
<glyph unicode="L" horiz-adv-x="1163" d="M184 0v1443h180v-1281h696v-162h-876z" />
<glyph unicode="M" horiz-adv-x="1790" d="M184 0v1443h209l494 -1194h16l494 1194h209v-1443h-168v1102h-12l-455 -1092h-152l-455 1092h-12v-1102h-168z" />
<glyph unicode="N" horiz-adv-x="1520" d="M184 0v1443h175l783 -1123h16v1123h178v-1443h-175l-783 1119h-16v-1119h-178z" />
<glyph unicode="O" horiz-adv-x="1579" d="M128 722q0 348 178.5 551.5t484.5 203.5q307 0 483.5 -203.5t176.5 -551.5t-176.5 -552t-483.5 -204q-309 0 -486 204t-177 552zM313 722q0 -271 127.5 -430.5t350.5 -159.5q221 0 348 160t127 430q0 272 -127 430.5t-348 158.5q-220 0 -349 -159t-129 -430z" />
<glyph unicode="P" horiz-adv-x="1301" d="M184 0v1443h545q205 0 333.5 -130.5t128.5 -336.5q0 -202 -130.5 -331.5t-333.5 -129.5h-363v-515h-180zM364 675h318q154 0 239 79t85 222q0 149 -83.5 228t-240.5 79h-318v-608z" />
<glyph unicode="Q" horiz-adv-x="1579" d="M128 721q0 349 178.5 552.5t484.5 203.5q304 0 482 -203.5t178 -552.5q0 -215 -66.5 -378t-189.5 -252l183 -254h-202l-127 173q-117 -44 -258 -44q-308 0 -485.5 202t-177.5 553zM313 721q0 -273 128.5 -431t350.5 -158q81 0 154 22l-191 259h204l131 -174 q85 68 130.5 192.5t45.5 289.5q0 272 -128 431t-347 159q-221 0 -349.5 -159t-128.5 -431z" />
<glyph unicode="R" horiz-adv-x="1338" d="M184 0v1443h551q209 0 334.5 -117.5t125.5 -312.5q0 -151 -77 -261t-213 -153l334 -599h-211l-309 571h-355v-571h-180zM364 731h355q139 0 214.5 71.5t75.5 204.5q0 130 -79 203t-219 73h-347v-552z" />
<glyph unicode="S" horiz-adv-x="1305" d="M115 377h182q17 -113 117 -180t254 -67q146 0 241.5 70.5t95.5 177.5q0 92 -70 152.5t-219 98.5l-185 48q-196 49 -285 142t-89 247q0 184 140.5 297.5t369.5 113.5q209 0 350 -112t152 -287h-181q-19 111 -105.5 173.5t-221.5 62.5q-144 0 -231.5 -66.5t-87.5 -175.5 q0 -85 64.5 -138t213.5 -91l151 -40q224 -56 321.5 -150t97.5 -253q0 -199 -146 -316.5t-393 -117.5q-229 0 -375.5 112t-160.5 299z" />
<glyph unicode="T" horiz-adv-x="1298" d="M94 1281v162h1110v-162h-465v-1281h-180v1281h-465z" />
<glyph unicode="U" horiz-adv-x="1524" d="M189 496v947h180v-935q0 -167 104.5 -271.5t288.5 -104.5t288.5 104.5t104.5 271.5v935h180v-947q0 -235 -154.5 -382.5t-418.5 -147.5t-418.5 147.5t-154.5 382.5z" />
<glyph unicode="V" horiz-adv-x="1380" d="M69 1443h189l424 -1210h16l424 1210h189l-532 -1443h-178z" />
<glyph unicode="W" horiz-adv-x="1982" d="M90 1443h188l287 -1173h12l323 1173h182l323 -1173h12l287 1173h188l-393 -1443h-170l-332 1139h-12l-332 -1139h-170z" />
<glyph unicode="X" horiz-adv-x="1390" d="M90 0l494 726l-488 717h217l380 -574h16l386 574h205l-500 -725l489 -718h-216l-378 569h-16l-385 -569h-204z" />
<glyph unicode="Y" horiz-adv-x="1344" d="M70 1443h205l389 -641h16l389 641h205l-512 -822v-621h-180v621z" />
<glyph unicode="Z" horiz-adv-x="1355" d="M144 0v128l817 1137v16h-789v162h1022v-128l-813 -1137v-16h830v-162h-1067z" />
<glyph unicode="[" horiz-adv-x="782" d="M268 -307v1813h434v-136h-266v-1541h266v-136h-434z" />
<glyph unicode="\" horiz-adv-x="682" d="M0 1506h152l530 -1813h-152z" />
<glyph unicode="]" horiz-adv-x="782" d="M80 -171h266v1541h-266v136h434v-1813h-434v136z" />
<glyph unicode="^" horiz-adv-x="984" d="M101 780l296 639h190l296 -639h-163l-220 500h-16l-220 -500h-163z" />
<glyph unicode="_" horiz-adv-x="916" d="M-10 -107h936v-128h-936v128z" />
<glyph unicode="`" horiz-adv-x="1093" d="M310 1601h220l252 -338h-194z" />
<glyph unicode="a" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM276 306q0 -81 61 -127.5t157 -46.5 q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125z" />
<glyph unicode="b" horiz-adv-x="1258" d="M172 0v1506h172v-598h16q44 87 135.5 138t202.5 51q206 0 329 -152t123 -406q0 -253 -123.5 -405.5t-328.5 -152.5q-112 0 -203.5 50.5t-142.5 140.5h-16v-172h-164zM340 539q0 -187 85.5 -295t232.5 -108q148 0 231 107t83 296t-83 296t-231 107q-147 0 -232.5 -108 t-85.5 -295z" />
<glyph unicode="c" horiz-adv-x="1146" d="M108 543q0 252 134 403t360 151q189 0 308.5 -99t143.5 -250h-174q-22 85 -94.5 139.5t-183.5 54.5q-145 0 -230.5 -108t-85.5 -291q0 -186 86 -296.5t230 -110.5q109 0 181.5 49t96.5 140h174q-24 -153 -143 -248.5t-308 -95.5q-227 0 -361 152.5t-134 409.5z" />
<glyph unicode="d" horiz-adv-x="1258" d="M108 539q0 253 123.5 405.5t329.5 152.5q113 0 202.5 -50.5t135.5 -138.5h15v598h172v-1506h-164v172h-16q-51 -90 -142 -140.5t-203 -50.5q-207 0 -330 152t-123 406zM286 539q0 -189 83.5 -296t231.5 -107q147 0 232 108t85 295q0 185 -85.5 294t-231.5 109 q-147 0 -231 -107.5t-84 -295.5z" />
<glyph unicode="e" horiz-adv-x="1170" d="M108 538q0 255 132 407t354 152q218 0 343 -145t125 -397v-66h-775v-8q7 -160 90.5 -254t220.5 -94q209 0 278 146h172q-39 -140 -158.5 -219t-295.5 -79q-224 0 -355 150.5t-131 406.5zM287 628h594q-3 143 -82 230t-207 87q-129 0 -212 -87t-93 -230z" />
<glyph unicode="f" horiz-adv-x="741" d="M55 934v144h179v120q0 318 313 318q65 0 116 -10v-140q-26 5 -81 5q-92 0 -134 -44t-42 -134v-115h247v-144h-247v-934h-172v934h-179z" />
<glyph unicode="g" horiz-adv-x="1248" d="M108 555q0 244 123 393t329 149q113 0 206 -51.5t140 -138.5h16v171h164v-1097q0 -181 -130 -288t-350 -107q-193 0 -312.5 80t-139.5 215h182q14 -67 85 -106t185 -39q145 0 226.5 67.5t81.5 186.5v212h-16q-51 -89 -139 -137.5t-199 -48.5q-208 0 -330 147.5 t-122 391.5zM286 555q0 -179 82 -283t230 -104q149 0 234.5 104.5t85.5 282.5q0 177 -86 282t-234 105t-230 -104t-82 -283z" />
<glyph unicode="h" horiz-adv-x="1216" d="M172 0v1506h172v-598h16q41 91 125.5 140t206.5 49q172 0 272 -105.5t100 -293.5v-698h-172v656q0 286 -249 286q-142 0 -220.5 -82.5t-78.5 -221.5v-638h-172z" />
<glyph unicode="i" horiz-adv-x="504" d="M132 1417q0 49 35 84.5t85 35.5t85 -35.5t35 -84.5t-35 -84.5t-85 -35.5t-85 35.5t-35 84.5zM166 0v1078h172v-1078h-172z" />
<glyph unicode="j" horiz-adv-x="504" d="M-22 -223h26q90 0 126 38t36 129v1134h172v-1131q0 -166 -80 -241.5t-250 -75.5h-30v147zM132 1417q0 49 35 84.5t85 35.5t85 -35.5t35 -84.5t-35 -84.5t-85 -35.5t-85 35.5t-35 84.5z" />
<glyph unicode="k" horiz-adv-x="1112" d="M172 0v1506h172v-899h16l468 471h211l-464 -462l495 -616h-219l-404 505l-103 -96v-409h-172z" />
<glyph unicode="l" horiz-adv-x="516" d="M172 0v1506h172v-1506h-172z" />
<glyph unicode="m" horiz-adv-x="1782" d="M162 0v1078h164v-170h16q37 90 115 139.5t186 49.5q106 0 182.5 -51t115.5 -138h16q44 87 132 138t198 51q165 0 254 -91.5t89 -260.5v-745h-172v705q0 237 -227 237q-110 0 -180 -72t-70 -177v-693h-172v732q0 96 -61.5 153t-163.5 57q-105 0 -177.5 -80t-72.5 -194 v-668h-172z" />
<glyph unicode="n" horiz-adv-x="1195" d="M162 0v1078h164v-170h16q86 189 324 189q181 0 279 -103.5t98 -295.5v-698h-172v656q0 145 -62.5 215.5t-191.5 70.5q-130 0 -206.5 -81.5t-76.5 -222.5v-638h-172z" />
<glyph unicode="o" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149t364 -149t133 -409q0 -259 -133.5 -408.5t-363.5 -149.5t-363.5 149.5t-133.5 408.5zM286 539q0 -192 83 -297.5t236 -105.5t236 105.5t83 297.5t-83 297.5t-236 105.5t-236 -105.5t-83 -297.5z" />
<glyph unicode="p" horiz-adv-x="1248" d="M162 -360v1438h164v-180h16q52 93 143.5 146t201.5 53q206 0 329.5 -152t123.5 -406q0 -253 -124 -405.5t-329 -152.5q-115 0 -204 47.5t-133 133.5h-16v-522h-172zM329 539q0 -185 85.5 -294t231.5 -109q148 0 232 107t84 296t-83.5 296t-231.5 107q-146 0 -232 -108.5 t-86 -294.5z" />
<glyph unicode="q" horiz-adv-x="1248" d="M108 539q0 253 123 405.5t328 152.5q110 0 203.5 -54t141.5 -145h16v180h166v-1438h-174v522h-16q-106 -181 -337 -181q-206 0 -328.5 151.5t-122.5 406.5zM286 539q0 -190 82.5 -296.5t229.5 -106.5q148 0 233.5 108.5t85.5 294.5q0 185 -86 294t-232 109 q-147 0 -230 -107t-83 -296z" />
<glyph unicode="r" horiz-adv-x="780" d="M162 0v1078h164v-160h16q24 79 108 129t191 50q49 0 86 -5v-172q-35 10 -110 10q-123 0 -203 -74t-80 -188v-668h-172z" />
<glyph unicode="s" horiz-adv-x="1072" d="M108 275h177q22 -70 87 -109.5t166 -39.5q109 0 179 47t70 121q0 110 -171 152l-185 45q-153 37 -223.5 106.5t-70.5 185.5q0 134 115 224t287 90q169 0 279 -78t127 -211h-170q-18 66 -79.5 104.5t-157.5 38.5q-97 0 -162.5 -45t-65.5 -116q0 -55 45.5 -91.5 t141.5 -59.5l166 -40q154 -37 227.5 -107t73.5 -185q0 -143 -123 -234.5t-309 -91.5q-180 0 -295 80t-129 214z" />
<glyph unicode="t" horiz-adv-x="744" d="M60 934v144h174v279h172v-279h240v-144h-240v-610q0 -95 38 -138t122 -43q37 0 80 4v-145q-57 -10 -101 -10q-167 0 -239 67t-72 221v654h-174z" />
<glyph unicode="u" horiz-adv-x="1196" d="M152 380v698h172v-656q0 -147 58 -215.5t186 -68.5q141 0 217.5 79.5t76.5 222.5v638h172v-1078h-164v169h-16q-41 -91 -126 -139.5t-208 -48.5q-180 0 -274 103t-94 296z" />
<glyph unicode="v" horiz-adv-x="1110" d="M68 1078h184l295 -888h16l295 888h184l-399 -1078h-176z" />
<glyph unicode="w" horiz-adv-x="1586" d="M68 1078h174l212 -862h16l241 862h165l241 -862h16l212 862h173l-302 -1078h-175l-241 834h-16l-240 -834h-174z" />
<glyph unicode="x" horiz-adv-x="1076" d="M73 1078h205l256 -401h16l253 401h195l-366 -532l371 -546h-204l-255 407h-16l-256 -407h-195l369 539z" />
<glyph unicode="y" horiz-adv-x="1112" d="M68 1078h186l296 -887h16l295 887h183l-421 -1144q-69 -187 -147 -255.5t-222 -68.5q-41 0 -77 6v147q26 -5 72 -5q75 0 119.5 40t78.5 137l20 64z" />
<glyph unicode="z" horiz-adv-x="1104" d="M137 0v126l622 792v16h-621v144h826v-129l-619 -789v-16h622v-144h-830z" />
<glyph unicode="{" horiz-adv-x="782" d="M80 505v190q124 0 172 46.5t48 166.5v254q0 175 85 259.5t262 84.5h55v-141h-40q-106 0 -152 -52.5t-46 -172.5v-294q0 -212 -219 -234v-24q219 -22 219 -234v-295q0 -120 46 -172.5t152 -52.5h40v-141h-55q-177 0 -262 84.5t-85 259.5v255q0 120 -48 166.5t-172 46.5z " />
<glyph unicode="|" horiz-adv-x="682" d="M268 -307v1813h146v-1813h-146z" />
<glyph unicode="}" horiz-adv-x="782" d="M80 -166h40q106 0 152 52.5t46 172.5v295q0 212 219 234v24q-219 22 -219 234v294q0 120 -46 172.5t-152 52.5h-40v141h55q177 0 262 -84.5t85 -259.5v-254q0 -120 48 -166.5t172 -46.5v-190q-124 0 -172 -46.5t-48 -166.5v-255q0 -175 -85 -259.5t-262 -84.5h-55v141z " />
<glyph unicode="~" horiz-adv-x="1202" d="M96 558q-1 161 79 252.5t226 91.5q64 0 122.5 -30t123.5 -95q52 -55 88 -77.5t70 -22.5q131 0 142 179h159q1 -161 -77.5 -252.5t-221.5 -91.5q-67 0 -125.5 29t-124.5 95q-55 56 -90.5 78.5t-69.5 22.5q-130 0 -141 -179h-160z" />
<glyph unicode="&#xa1;" horiz-adv-x="637" d="M180 981q0 61 39 100t100 39t99.5 -39t38.5 -100q0 -62 -38.5 -101t-99.5 -39t-100 39.5t-39 100.5zM218 -342l17 964h166l17 -964h-200z" />
<glyph unicode="&#xa2;" d="M161 725q0 224 120 371t315 176v171h172v-169q159 -21 268.5 -113.5t130.5 -230.5h-174q-31 92 -112 143t-196 51q-149 0 -247.5 -110t-98.5 -289q0 -182 99 -294t247 -112q126 0 205 49t103 140h174q-22 -140 -129.5 -230t-269.5 -109v-169h-172v171q-195 30 -315 178.5 t-120 375.5z" />
<glyph unicode="&#xa3;" d="M128 0v152q133 30 220.5 129.5t87.5 219.5q0 66 -24 152h-284v144h234q-61 171 -61 263q0 196 137.5 306.5t379.5 110.5q187 0 300 -53v-160q-38 22 -121.5 38t-169.5 16q-161 0 -251.5 -67.5t-90.5 -186.5q0 -97 60 -267h471v-144h-424q21 -84 21 -145q0 -117 -59 -205 t-150 -135v-14h786v-154h-1062z" />
<glyph unicode="&#xa4;" d="M171 268l170 170q-67 100 -67 218t67 217l-170 170l106 106l169 -169q100 67 219 67t218 -67l168 169l106 -106l-168 -169q67 -99 67 -218q0 -121 -68 -219l169 -169l-106 -106l-169 169q-97 -66 -217 -66q-119 0 -219 67l-169 -170zM401 656q0 -109 77.5 -186.5 t186.5 -77.5t186.5 77.5t77.5 186.5t-77.5 186.5t-186.5 77.5t-186.5 -77.5t-77.5 -186.5z" />
<glyph unicode="&#xa5;" d="M62 1443h205l389 -641h16l389 641h205l-458 -736h223v-136h-277v-166h277v-136h-277v-269h-180v269h-277v136h277v166h-277v136h223z" />
<glyph unicode="&#xa6;" horiz-adv-x="682" d="M268 -307v725h146v-725h-146zM268 781v725h146v-725h-146z" />
<glyph unicode="&#xa7;" horiz-adv-x="1032" d="M103 533q0 95 58.5 164t164.5 98v16q-219 85 -219 273q0 140 111 226.5t290 86.5q167 0 274 -78t124 -211h-173q-11 65 -73 104t-156 39q-101 0 -162.5 -43.5t-61.5 -113.5q0 -57 46 -95t149 -66l141 -38q162 -43 237.5 -111t75.5 -169q0 -103 -71 -182t-183 -101v-16 q254 -57 254 -257q0 -139 -114.5 -227.5t-293.5 -88.5q-172 0 -285.5 83t-123.5 216h176q10 -69 77 -111t165 -42q102 0 165.5 44t63.5 114q0 56 -47 94t-151 66l-141 38q-164 44 -240.5 113.5t-76.5 174.5zM280 567q0 -62 44.5 -105.5t126.5 -61.5l83 -18q92 0 155 55 t63 136q0 63 -44 106t-126 61l-83 18q-91 0 -155 -56t-64 -135z" />
<glyph unicode="&#xa8;" horiz-adv-x="1092" d="M223 1384q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM627 1384q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1742" d="M78 626q0 222 104 404t286 286t403 104q166 0 313.5 -61t252.5 -166.5t166 -253t61 -313.5q0 -222 -104 -404t-286 -286t-403 -104q-165 0 -312.5 61.5t-252.5 167t-166.5 252.5t-61.5 313zM219 626q0 -137 50.5 -258t137 -208t207.5 -137.5t257 -50.5q137 0 258 50.5 t207.5 137t136.5 208t50 258.5q0 183 -85 333t-234.5 235.5t-332.5 85.5q-137 0 -258 -50.5t-207.5 -137t-136.5 -208t-50 -258.5zM493 628q0 192 107 306t288 114q143 0 242 -77t119 -203h-139q-18 71 -78.5 113.5t-143.5 42.5q-116 0 -184.5 -80t-68.5 -216 q0 -138 68.5 -219t184.5 -81q88 0 146 39.5t76 111.5h139q-19 -126 -116.5 -200.5t-243.5 -74.5q-181 0 -288.5 115t-107.5 309z" />
<glyph unicode="&#xaa;" horiz-adv-x="942" d="M91 794q0 123 87.5 194t251.5 81l245 15v80q0 75 -52 116t-147 41q-75 0 -125 -29.5t-60 -80.5h-165q11 115 108 183.5t248 68.5q167 0 261 -81t94 -224v-618h-155v119h-16q-40 -69 -112 -106.5t-164 -37.5q-134 0 -216.5 77t-82.5 202zM260 800q0 -67 48.5 -107.5 t129.5 -40.5q103 0 171.5 62.5t68.5 154.5v96l-227 -17q-94 -6 -142.5 -43.5t-48.5 -104.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1391" d="M179 561v8l350 416h204l-351 -412v-16l351 -412h-204zM657 561v8l351 416h204l-351 -412v-16l351 -412h-205z" />
<glyph unicode="&#xac;" d="M190 506v148h948v-528h-148v380h-800z" />
<glyph unicode="&#xad;" horiz-adv-x="966" d="M155 499v163h656v-163h-656z" />
<glyph unicode="&#xae;" horiz-adv-x="1260" d="M108 971q0 144 69.5 264t189.5 189t263 69q108 0 204.5 -41t166 -110.5t110.5 -166t41 -204.5q0 -143 -69 -263t-189 -189.5t-264 -69.5q-218 0 -370 152t-152 370zM212 971q0 -175 121.5 -296.5t296.5 -121.5t296.5 121.5t121.5 296.5t-121 296.5t-297 121.5 q-175 0 -296.5 -121.5t-121.5 -296.5zM429 733v499h249q75 0 124.5 -43.5t49.5 -109.5q0 -122 -116 -150l138 -196h-120l-129 183h-98v-183h-98zM527 1000h124q92 0 92 74q0 69 -89 69h-127v-143z" />
<glyph unicode="&#xaf;" horiz-adv-x="1048" d="M187 1286v136h674v-136h-674z" />
<glyph unicode="&#xb0;" horiz-adv-x="901" d="M126 1138q0 137 92 231t232 94t232.5 -94.5t92.5 -230.5t-92.5 -230.5t-232.5 -94.5t-232 94t-92 231zM253 1138q0 -83 54.5 -140t142.5 -57q89 0 144 57t55 140q0 84 -55.5 140.5t-143.5 56.5t-142.5 -56.5t-54.5 -140.5z" />
<glyph unicode="&#xb1;" d="M155 112v148h1018v-148h-1018zM155 662v136h435v291h148v-291h435v-136h-435v-291h-148v291h-435z" />
<glyph unicode="&#xb2;" horiz-adv-x="825" d="M95 1537v2q0 126 87.5 206t226.5 80q135 0 221.5 -73t86.5 -187q0 -70 -42.5 -140t-160.5 -188l-185 -186v-16h401v-135h-627v110l301 310q91 92 122 139.5t31 96.5q0 60 -43 99.5t-110 39.5q-70 0 -113.5 -43t-43.5 -113v-2h-152z" />
<glyph unicode="&#xb3;" horiz-adv-x="853" d="M90 1147h156q5 -63 54 -100.5t127 -37.5q77 0 126 40.5t49 104.5q0 68 -48 107t-131 39h-112v122h109q69 0 112 38.5t43 99.5t-42 97t-113 36q-69 0 -112.5 -37.5t-48.5 -101.5h-151q5 124 90.5 196.5t226.5 72.5q132 0 219.5 -68.5t87.5 -172.5q0 -77 -45.5 -133.5 t-120.5 -72.5v-16q92 -7 147 -66t55 -149q0 -116 -97 -193t-245 -77q-146 0 -238.5 74.5t-97.5 197.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1093" d="M286 1263l252 338h220l-278 -338h-194z" />
<glyph unicode="&#xb5;" horiz-adv-x="1258" d="M162 -360v1438h172v-654q0 -286 244 -286q130 0 207 81t77 221v638h172v-809q0 -73 22 -100.5t78 -27.5q25 0 49 4v-145q-37 -10 -82 -10q-111 0 -162 37.5t-67 130.5l-2 12h-16q-43 -96 -110.5 -142.5t-173.5 -46.5q-160 0 -220 129h-16v-470h-172z" />
<glyph unicode="&#xb6;" horiz-adv-x="1321" d="M160 1002q0 191 136.5 316t338.5 125h199v-1663h-180v795h-20q-202 0 -338 121.5t-136 305.5zM981 -220v1663h180v-1663h-180z" />
<glyph unicode="&#xb7;" horiz-adv-x="630" d="M182 738q0 55 39.5 94t93.5 39q55 0 94 -39t39 -94q0 -54 -39 -93.5t-94 -39.5q-54 0 -93.5 39.5t-39.5 93.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1093" d="M361 -358q48 -14 122 -14q119 0 119 73q0 35 -35 50t-114 15h-59v84l70 172h123v-22l-51 -132q102 0 158.5 -43t56.5 -120q0 -85 -65.5 -137t-178.5 -52q-85 0 -146 18v108z" />
<glyph unicode="&#xb9;" horiz-adv-x="664" d="M85 1484v152q87 65 236 167h156v-903h-156v742h-16q-14 -9 -116 -82.5t-104 -75.5z" />
<glyph unicode="&#xba;" horiz-adv-x="1024" d="M88 989q0 220 113.5 347t310.5 127t310.5 -126.5t113.5 -347.5t-113.5 -347.5t-310.5 -126.5t-310.5 126.5t-113.5 347.5zM262 989q0 -157 65.5 -243.5t184.5 -86.5t185 86.5t66 243.5q0 158 -65.5 244t-185.5 86q-119 0 -184.5 -86.5t-65.5 -243.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1391" d="M179 145l351 412v16l-351 412h204l351 -416v-8l-350 -416h-205zM658 145l351 412v16l-351 412h204l350 -416v-8l-350 -416h-204z" />
<glyph unicode="&#xbc;" horiz-adv-x="1944" d="M85 1124v152q87 65 236 167h156v-903h-156v742h-16q-14 -9 -116 -82.5t-104 -75.5zM504 0l335 758l302 685h167l-318 -721l-319 -722h-167zM1135 170v141q77 141 378 592h230v-600h121v-133h-121v-170h-156v170h-452zM1285 299h304v477h-6q-203 -300 -298 -469v-8z" />
<glyph unicode="&#xbd;" horiz-adv-x="1949" d="M85 1124v152q87 65 236 167h156v-903h-156v742h-16q-14 -9 -116 -82.5t-104 -75.5zM504 0l335 758l302 685h167l-318 -721l-319 -722h-167zM1219 637v2q0 126 87.5 206t226.5 80q135 0 221.5 -73t86.5 -187q0 -70 -42.5 -140t-160.5 -188l-185 -186v-16h401v-135h-627 v110l301 310q91 92 122 139.5t31 96.5q0 60 -43 99.5t-110 39.5q-70 0 -113.5 -43t-43.5 -113v-2h-152z" />
<glyph unicode="&#xbe;" horiz-adv-x="2134" d="M90 787h156q5 -63 54 -100.5t127 -37.5q77 0 126 40.5t49 104.5q0 68 -48 107t-131 39h-112v122h109q69 0 112 38.5t43 99.5t-42 97t-113 36q-69 0 -112.5 -37.5t-48.5 -101.5h-151q5 124 90.5 196.5t226.5 72.5q132 0 219.5 -68.5t87.5 -172.5q0 -77 -45.5 -133.5 t-120.5 -72.5v-16q92 -7 147 -66t55 -149q0 -116 -97 -193t-245 -77q-146 0 -238.5 74.5t-97.5 197.5zM694 0l335 758l302 685h167l-318 -721l-319 -722h-167zM1325 170v141q46 85 122.5 204.5t255.5 387.5h230v-600h121v-133h-121v-170h-156v170h-452zM1475 299h304v477h-6 q-203 -300 -298 -469v-8z" />
<glyph unicode="&#xbf;" horiz-adv-x="1050" d="M110 10q0 216 209 343q94 56 128 106t34 130v65h175v-92q1 -98 -46.5 -169.5t-152.5 -134.5q-87 -54 -124 -107.5t-37 -131.5q0 -96 67.5 -156.5t178.5 -60.5q112 0 180.5 62.5t76.5 170.5h171q-8 -171 -121 -280t-314 -109q-188 0 -306.5 103t-118.5 261zM418 979 q0 60 39.5 100t99.5 40t99 -40t39 -100q0 -61 -38.5 -100t-99.5 -39t-100 39t-39 100z" />
<glyph unicode="&#xc0;" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM252 1873h226l310 -298h-204zM456 566h468l-226 644h-16z" />
<glyph unicode="&#xc1;" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM456 566h468l-226 644h-16zM584 1575l310 298h226l-332 -298h-204z" />
<glyph unicode="&#xc2;" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM308 1575l300 298h164l300 -298h-202l-172 177h-16l-172 -177h-202zM456 566h468l-226 644h-16z" />
<glyph unicode="&#xc3;" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM311 1580q0 136 61.5 213.5t170.5 77.5q44 0 82.5 -16t64.5 -39.5t48 -46.5t44.5 -39t44.5 -16q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5t-170.5 -77.5q-44 0 -82.5 16t-64.5 39t-48 46.5 t-44.5 39.5t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142zM456 566h468l-226 644h-16z" />
<glyph unicode="&#xc4;" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM367 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM456 566h468l-226 644h-16zM771 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5 t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1380" d="M69 0l532 1443h178l532 -1443h-189l-145 413h-574l-145 -413h-189zM456 566h468l-226 644h-16zM491 1748q0 83 58 140t141 57t141 -57t58 -140t-58 -140t-141 -57t-141 57t-58 140zM585 1748q0 -45 29.5 -75t75.5 -30t75.5 30t29.5 75q0 46 -29.5 75.5t-75.5 29.5 t-75.5 -29.5t-29.5 -75.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1966" d="M69 0l651 1443h1104v-162h-658v-473h621v-160h-621v-486h658v-162h-838v459h-527l-209 -459h-181zM529 612h457v680h-147z" />
<glyph unicode="&#xc7;" horiz-adv-x="1466" d="M128 721q0 349 171 552.5t465 203.5q228 0 390 -130t194 -339h-183q-36 140 -144 221.5t-257 81.5q-208 0 -329.5 -159t-121.5 -431t122 -430.5t330 -158.5q151 0 258.5 72.5t141.5 197.5h183q-34 -185 -174 -301t-347 -133l-39 -100q102 0 158.5 -43t56.5 -120 q0 -85 -65.5 -137t-178.5 -52q-85 0 -146 18v108q48 -14 122 -14q119 0 119 73q0 35 -35 50t-114 15h-59v84l49 119q-264 25 -415.5 225.5t-151.5 526.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1220" d="M184 0v1443h894v-162h-714v-463h677v-160h-677v-496h714v-162h-894zM193 1873h226l310 -298h-204z" />
<glyph unicode="&#xc9;" horiz-adv-x="1220" d="M184 0v1443h894v-162h-714v-463h677v-160h-677v-496h714v-162h-894zM525 1575l310 298h226l-332 -298h-204z" />
<glyph unicode="&#xca;" horiz-adv-x="1220" d="M184 0v1443h894v-162h-714v-463h677v-160h-677v-496h714v-162h-894zM249 1575l300 298h164l300 -298h-202l-172 177h-16l-172 -177h-202z" />
<glyph unicode="&#xcb;" horiz-adv-x="1220" d="M184 0v1443h894v-162h-714v-463h677v-160h-677v-496h714v-162h-894zM308 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM712 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5 t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="548" d="M-164 1873h226l310 -298h-204zM184 0v1443h180v-1443h-180z" />
<glyph unicode="&#xcd;" horiz-adv-x="548" d="M168 1575l310 298h226l-332 -298h-204zM184 0v1443h180v-1443h-180z" />
<glyph unicode="&#xce;" horiz-adv-x="548" d="M-108 1575l300 298h164l300 -298h-202l-172 177h-16l-172 -177h-202zM184 0v1443h180v-1443h-180z" />
<glyph unicode="&#xcf;" horiz-adv-x="548" d="M-49 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM184 0v1443h180v-1443h-180zM355 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1487" d="M26 663v163h158v617h495q321 0 501 -190.5t180 -530.5q0 -341 -179.5 -531.5t-501.5 -190.5h-495v663h-158zM364 162h303q241 0 375 147t134 411q0 265 -134.5 413t-374.5 148h-303v-455h248v-163h-248v-501z" />
<glyph unicode="&#xd1;" horiz-adv-x="1520" d="M184 0v1443h175l783 -1123h16v1123h178v-1443h-175l-783 1119h-16v-1119h-178zM381 1580q0 136 61.5 213.5t170.5 77.5q44 0 82.5 -16t64.5 -39.5t48 -46.5t44.5 -39t44.5 -16q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5t-170.5 -77.5q-44 0 -82.5 16t-64.5 39 t-48 46.5t-44.5 39.5t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142z" />
<glyph unicode="&#xd2;" horiz-adv-x="1579" d="M128 722q0 348 178.5 551.5t484.5 203.5q307 0 483.5 -203.5t176.5 -551.5t-176.5 -552t-483.5 -204q-309 0 -486 204t-177 552zM313 722q0 -271 127.5 -430.5t350.5 -159.5q221 0 348 160t127 430q0 272 -127 430.5t-348 158.5q-220 0 -349 -159t-129 -430zM353 1873 h226l310 -298h-204z" />
<glyph unicode="&#xd3;" horiz-adv-x="1579" d="M128 722q0 348 178.5 551.5t484.5 203.5q307 0 483.5 -203.5t176.5 -551.5t-176.5 -552t-483.5 -204q-309 0 -486 204t-177 552zM313 722q0 -271 127.5 -430.5t350.5 -159.5q221 0 348 160t127 430q0 272 -127 430.5t-348 158.5q-220 0 -349 -159t-129 -430zM685 1575 l310 298h226l-332 -298h-204z" />
<glyph unicode="&#xd4;" horiz-adv-x="1579" d="M128 722q0 348 178.5 551.5t484.5 203.5q307 0 483.5 -203.5t176.5 -551.5t-176.5 -552t-483.5 -204q-309 0 -486 204t-177 552zM313 722q0 -271 127.5 -430.5t350.5 -159.5q221 0 348 160t127 430q0 272 -127 430.5t-348 158.5q-220 0 -349 -159t-129 -430zM409 1575 l300 298h164l300 -298h-202l-172 177h-16l-172 -177h-202z" />
<glyph unicode="&#xd5;" horiz-adv-x="1579" d="M128 722q0 348 178.5 551.5t484.5 203.5q307 0 483.5 -203.5t176.5 -551.5t-176.5 -552t-483.5 -204q-309 0 -486 204t-177 552zM313 722q0 -271 127.5 -430.5t350.5 -159.5q221 0 348 160t127 430q0 272 -127 430.5t-348 158.5q-220 0 -349 -159t-129 -430zM412 1580 q0 136 61.5 213.5t170.5 77.5q44 0 82.5 -16t64.5 -39.5t48 -46.5t44.5 -39t44.5 -16q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5t-170.5 -77.5q-44 0 -82.5 16t-64.5 39t-48 46.5t-44.5 39.5t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142z" />
<glyph unicode="&#xd6;" horiz-adv-x="1579" d="M128 722q0 348 178.5 551.5t484.5 203.5q307 0 483.5 -203.5t176.5 -551.5t-176.5 -552t-483.5 -204q-309 0 -486 204t-177 552zM313 722q0 -271 127.5 -430.5t350.5 -159.5q221 0 348 160t127 430q0 272 -127 430.5t-348 158.5q-220 0 -349 -159t-129 -430zM468 1696 q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM872 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xd7;" d="M252 272l307 308l-307 308l104 104l308 -307l308 307l104 -104l-307 -308l307 -308l-104 -104l-308 307l-308 -307z" />
<glyph unicode="&#xd8;" horiz-adv-x="1551" d="M128 722q0 348 178.5 551.5t484.5 203.5q231 0 392 -119l101 130l98 -69l-110 -143q179 -202 179 -554q0 -348 -176.5 -552t-483.5 -204q-248 0 -418 137l-115 -150l-100 76l127 165q-157 200 -157 528zM313 722q0 -227 90 -376l672 871q-117 94 -284 94 q-220 0 -349 -159t-129 -430zM483 246q122 -114 308 -114q221 0 348 160t127 430q0 249 -108 405z" />
<glyph unicode="&#xd9;" horiz-adv-x="1524" d="M189 496v947h180v-935q0 -167 104.5 -271.5t288.5 -104.5t288.5 104.5t104.5 271.5v935h180v-947q0 -235 -154.5 -382.5t-418.5 -147.5t-418.5 147.5t-154.5 382.5zM324 1873h226l310 -298h-204z" />
<glyph unicode="&#xda;" horiz-adv-x="1524" d="M189 496v947h180v-935q0 -167 104.5 -271.5t288.5 -104.5t288.5 104.5t104.5 271.5v935h180v-947q0 -235 -154.5 -382.5t-418.5 -147.5t-418.5 147.5t-154.5 382.5zM656 1575l310 298h226l-332 -298h-204z" />
<glyph unicode="&#xdb;" horiz-adv-x="1524" d="M189 496v947h180v-935q0 -167 104.5 -271.5t288.5 -104.5t288.5 104.5t104.5 271.5v935h180v-947q0 -235 -154.5 -382.5t-418.5 -147.5t-418.5 147.5t-154.5 382.5zM380 1575l300 298h164l300 -298h-202l-172 177h-16l-172 -177h-202z" />
<glyph unicode="&#xdc;" horiz-adv-x="1524" d="M189 496v947h180v-935q0 -167 104.5 -271.5t288.5 -104.5t288.5 104.5t104.5 271.5v935h180v-947q0 -235 -154.5 -382.5t-418.5 -147.5t-418.5 147.5t-154.5 382.5zM439 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5 t-35.5 85.5zM843 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1344" d="M70 1443h205l389 -641h16l389 641h205l-512 -822v-621h-180v621zM566 1575l310 298h226l-332 -298h-204z" />
<glyph unicode="&#xde;" horiz-adv-x="1280" d="M184 0v1443h180v-266h373q83 0 159 -28t134.5 -80t93.5 -131.5t35 -175.5t-35 -176t-94.5 -132t-135.5 -80.5t-159 -28.5h-371v-345h-180zM364 505h322q130 0 209 66t79 191t-79 190t-209 65h-322v-512z" />
<glyph unicode="&#xdf;" horiz-adv-x="1274" d="M225 0v1101q0 182 115.5 288.5t303.5 106.5q183 0 299 -105t116 -270q0 -121 -65 -212t-164 -122v-18q156 -15 246 -108.5t90 -253.5q0 -181 -126 -301t-329 -120q-83 0 -144 13v153q61 -14 132 -14q139 0 217.5 75t78.5 196q0 125 -89.5 197t-250.5 72h-61v170h51 q113 0 179 68.5t66 195.5q0 100 -66 166t-177 66q-117 0 -184 -68.5t-67 -191.5v-1084h-171z" />
<glyph unicode="&#xe0;" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM214 1601h220l252 -338h-194zM276 306 q0 -81 61 -127.5t157 -46.5q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125z" />
<glyph unicode="&#xe1;" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM276 306q0 -81 61 -127.5t157 -46.5 q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125zM450 1263l252 338h220l-278 -338h-194z" />
<glyph unicode="&#xe2;" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM181 1228l282 340h202l282 -340h-198 l-177 227h-16l-177 -227h-198zM276 306q0 -81 61 -127.5t157 -46.5q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125z" />
<glyph unicode="&#xe3;" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM186 1231q0 136 61.5 213.5 t170.5 77.5q44 0 82.5 -16t64.5 -39t48 -46.5t44.5 -39.5t44.5 -16q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5t-170.5 -77.5q-44 0 -82.5 16t-64.5 39.5t-48 46.5t-44.5 39t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142zM276 306q0 -81 61 -127.5t157 -46.5 q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125z" />
<glyph unicode="&#xe4;" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM241 1349q0 50 35.5 85.5t85.5 35.5 t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM276 306q0 -81 61 -127.5t157 -46.5q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125zM645 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5 t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1130" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 129 133.5 208t289.5 79q196 0 303 -95t107 -264v-738h-164v159h-16q-52 -86 -138 -132t-194 -46q-159 0 -261.5 87.5t-102.5 233.5zM276 306q0 -81 61 -127.5t157 -46.5 q133 0 220.5 77t87.5 193v99l-290 -19q-122 -8 -179 -51t-57 -125zM332 1457q0 96 68 162.5t164 66.5t164 -66.5t68 -162.5t-68 -162.5t-164 -66.5t-164 66.5t-68 162.5zM442 1457q0 -53 35 -88t87 -35t87 35t35 88t-35 88t-87 35t-87 -35t-35 -88z" />
<glyph unicode="&#xe6;" horiz-adv-x="1849" d="M98 302q0 144 102 226t291 93l311 19v98q0 100 -62 153t-182 53q-97 0 -161 -35t-82 -99h-174q17 130 133.5 208.5t289.5 78.5q124 0 214.5 -50.5t133.5 -134.5h16q48 81 145.5 133t211.5 52q214 0 335 -140.5t121 -388.5v-68h-768v-18q7 -159 93 -253.5t225 -94.5 q100 0 164.5 35.5t99.5 109.5h172q-41 -143 -155.5 -220.5t-284.5 -77.5q-129 0 -236 65t-150 166h-16q-53 -109 -158.5 -170t-247.5 -61q-171 0 -276 87t-105 234zM276 306q0 -81 62.5 -127.5t165.5 -46.5q128 0 213 80.5t85 197.5v91l-290 -19q-122 -8 -179 -51t-57 -125z M973 639h587q-3 135 -82.5 221t-201.5 86q-130 0 -211.5 -82.5t-91.5 -224.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1146" d="M108 543q0 252 134 403t360 151q189 0 308.5 -99t143.5 -250h-174q-22 85 -94.5 139.5t-183.5 54.5q-145 0 -230.5 -108t-85.5 -291q0 -186 86 -296.5t230 -110.5q109 0 181.5 49t96.5 140h174q-22 -139 -122 -231t-260 -109l-45 -117q102 0 158.5 -43t56.5 -120 q0 -85 -65.5 -137t-178.5 -52q-84 0 -146 18v108q48 -14 122 -14q119 0 119 73q0 35 -35 50t-114 15h-59v84l55 134q-200 22 -316 171.5t-116 387.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1170" d="M108 538q0 255 132 407t354 152q218 0 343 -145t125 -397v-66h-775v-8q7 -160 90.5 -254t220.5 -94q209 0 278 146h172q-39 -140 -158.5 -219t-295.5 -79q-224 0 -355 150.5t-131 406.5zM244 1601h220l252 -338h-194zM287 628h594q-3 143 -82 230t-207 87 q-129 0 -212 -87t-93 -230z" />
<glyph unicode="&#xe9;" horiz-adv-x="1170" d="M108 538q0 255 132 407t354 152q218 0 343 -145t125 -397v-66h-775v-8q7 -160 90.5 -254t220.5 -94q209 0 278 146h172q-39 -140 -158.5 -219t-295.5 -79q-224 0 -355 150.5t-131 406.5zM287 628h594q-3 143 -82 230t-207 87q-129 0 -212 -87t-93 -230zM480 1263l252 338 h220l-278 -338h-194z" />
<glyph unicode="&#xea;" horiz-adv-x="1170" d="M108 538q0 255 132 407t354 152q218 0 343 -145t125 -397v-66h-775v-8q7 -160 90.5 -254t220.5 -94q209 0 278 146h172q-39 -140 -158.5 -219t-295.5 -79q-224 0 -355 150.5t-131 406.5zM211 1228l282 340h202l282 -340h-198l-177 227h-16l-177 -227h-198zM287 628h594 q-3 143 -82 230t-207 87q-129 0 -212 -87t-93 -230z" />
<glyph unicode="&#xeb;" horiz-adv-x="1170" d="M108 538q0 255 132 407t354 152q218 0 343 -145t125 -397v-66h-775v-8q7 -160 90.5 -254t220.5 -94q209 0 278 146h172q-39 -140 -158.5 -219t-295.5 -79q-224 0 -355 150.5t-131 406.5zM271 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5 t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM287 628h594q-3 143 -82 230t-207 87q-129 0 -212 -87t-93 -230zM675 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xec;" horiz-adv-x="504" d="M-98 1601h220l252 -338h-194zM166 0v1078h172v-1078h-172z" />
<glyph unicode="&#xed;" horiz-adv-x="504" d="M138 1263l252 338h220l-278 -338h-194zM166 0v1078h172v-1078h-172z" />
<glyph unicode="&#xee;" horiz-adv-x="504" d="M-131 1228l282 340h202l282 -340h-198l-177 227h-16l-177 -227h-198zM166 0v1078h172v-1078h-172z" />
<glyph unicode="&#xef;" horiz-adv-x="504" d="M-71 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM166 0v1078h172v-1078h-172zM333 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1188" d="M108 519q0 241 125 389.5t328 148.5q101 0 192 -48t126 -121h15q-45 183 -182 351l-278 -90l-35 112l231 75q-62 73 -200 190h206q77 -65 145 -141l250 81l35 -112l-209 -68q110 -149 168.5 -333t58.5 -382q0 -273 -130 -431.5t-353 -158.5q-229 0 -361 144t-132 394z M286 519q0 -175 85.5 -279t229.5 -104q145 0 231 104t86 279q0 173 -87 278t-230 105q-144 0 -229.5 -104t-85.5 -279z" />
<glyph unicode="&#xf1;" horiz-adv-x="1195" d="M162 0v1078h164v-170h16q86 189 324 189q181 0 279 -103.5t98 -295.5v-698h-172v656q0 145 -62.5 215.5t-191.5 70.5q-130 0 -206.5 -81.5t-76.5 -222.5v-638h-172zM225 1231q0 136 61.5 213.5t170.5 77.5q44 0 82.5 -16t64.5 -39t48 -46.5t44.5 -39.5t44.5 -16 q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5t-170.5 -77.5q-44 0 -82.5 16t-64.5 39.5t-48 46.5t-44.5 39t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142z" />
<glyph unicode="&#xf2;" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149t364 -149t133 -409q0 -259 -133.5 -408.5t-363.5 -149.5t-363.5 149.5t-133.5 408.5zM255 1601h220l252 -338h-194zM286 539q0 -192 83 -297.5t236 -105.5t236 105.5t83 297.5t-83 297.5t-236 105.5t-236 -105.5t-83 -297.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149t364 -149t133 -409q0 -259 -133.5 -408.5t-363.5 -149.5t-363.5 149.5t-133.5 408.5zM286 539q0 -192 83 -297.5t236 -105.5t236 105.5t83 297.5t-83 297.5t-236 105.5t-236 -105.5t-83 -297.5zM491 1263l252 338h220l-278 -338h-194z" />
<glyph unicode="&#xf4;" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149t364 -149t133 -409q0 -259 -133.5 -408.5t-363.5 -149.5t-363.5 149.5t-133.5 408.5zM222 1228l282 340h202l282 -340h-198l-177 227h-16l-177 -227h-198zM286 539q0 -192 83 -297.5t236 -105.5t236 105.5t83 297.5t-83 297.5t-236 105.5 t-236 -105.5t-83 -297.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149t364 -149t133 -409q0 -259 -133.5 -408.5t-363.5 -149.5t-363.5 149.5t-133.5 408.5zM227 1231q0 136 61.5 213.5t170.5 77.5q44 0 82.5 -16t64.5 -39t48 -46.5t44.5 -39.5t44.5 -16q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5 t-170.5 -77.5q-44 0 -82.5 16t-64.5 39.5t-48 46.5t-44.5 39t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142zM286 539q0 -192 83 -297.5t236 -105.5t236 105.5t83 297.5t-83 297.5t-236 105.5t-236 -105.5t-83 -297.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149t364 -149t133 -409q0 -259 -133.5 -408.5t-363.5 -149.5t-363.5 149.5t-133.5 408.5zM282 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM286 539q0 -192 83 -297.5t236 -105.5 t236 105.5t83 297.5t-83 297.5t-236 105.5t-236 -105.5t-83 -297.5zM686 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xf7;" d="M155 506v148h1018v-148h-1018zM539 223q0 54 36 89.5t90 35.5q53 0 88.5 -35.5t35.5 -89.5q0 -55 -35.5 -90t-88.5 -35q-54 0 -90 35.5t-36 89.5zM539 937q0 54 36 89.5t90 35.5q53 0 88.5 -35.5t35.5 -89.5q0 -55 -35.5 -90t-88.5 -35q-54 0 -90 35.5t-36 89.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1210" d="M108 539q0 260 133 409t364 149q144 0 256 -62l57 83l100 -64l-62 -92q146 -149 146 -423q0 -259 -133.5 -408.5t-363.5 -149.5q-155 0 -268 69l-59 -87l-104 69l67 98q-133 150 -133 409zM286 539q0 -158 58 -259l425 620q-69 42 -164 42q-153 0 -236 -105.5t-83 -297.5 zM429 186q73 -50 176 -50q153 0 236 105.5t83 297.5q0 175 -68 276z" />
<glyph unicode="&#xf9;" horiz-adv-x="1196" d="M152 380v698h172v-656q0 -147 58 -215.5t186 -68.5q141 0 217.5 79.5t76.5 222.5v638h172v-1078h-164v169h-16q-41 -91 -126 -139.5t-208 -48.5q-180 0 -274 103t-94 296zM243 1601h220l252 -338h-194z" />
<glyph unicode="&#xfa;" horiz-adv-x="1196" d="M152 380v698h172v-656q0 -147 58 -215.5t186 -68.5q141 0 217.5 79.5t76.5 222.5v638h172v-1078h-164v169h-16q-41 -91 -126 -139.5t-208 -48.5q-180 0 -274 103t-94 296zM479 1263l252 338h220l-278 -338h-194z" />
<glyph unicode="&#xfb;" horiz-adv-x="1196" d="M152 380v698h172v-656q0 -147 58 -215.5t186 -68.5q141 0 217.5 79.5t76.5 222.5v638h172v-1078h-164v169h-16q-41 -91 -126 -139.5t-208 -48.5q-180 0 -274 103t-94 296zM210 1228l282 340h202l282 -340h-198l-177 227h-16l-177 -227h-198z" />
<glyph unicode="&#xfc;" horiz-adv-x="1196" d="M152 380v698h172v-656q0 -147 58 -215.5t186 -68.5q141 0 217.5 79.5t76.5 222.5v638h172v-1078h-164v169h-16q-41 -91 -126 -139.5t-208 -48.5q-180 0 -274 103t-94 296zM270 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5 t-35.5 85.5zM674 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1112" d="M68 1078h186l296 -887h16l295 887h183l-421 -1144q-69 -187 -147 -255.5t-222 -68.5q-41 0 -77 6v147q26 -5 72 -5q75 0 119.5 40t78.5 137l20 64zM437 1263l252 338h220l-278 -338h-194z" />
<glyph unicode="&#xfe;" horiz-adv-x="1258" d="M172 -360v1866h164v-608h16q52 93 143.5 146t201.5 53q206 0 329.5 -152t123.5 -406q0 -253 -124 -405.5t-329 -152.5q-115 0 -204 47.5t-133 133.5h-16v-522h-172zM339 539q0 -185 85.5 -294t231.5 -109q148 0 232 107t84 296t-83.5 296t-231.5 107q-146 0 -232 -108.5 t-86 -294.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1112" d="M68 1078h186l296 -887h16l295 887h183l-421 -1144q-69 -187 -147 -255.5t-222 -68.5q-41 0 -77 6v147q26 -5 72 -5q75 0 119.5 40t78.5 137l20 64zM228 1349q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM632 1349 q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2325" d="M128 722q0 347 176.5 551t476.5 204q152 0 288.5 -75t203.5 -196h16v237h894v-162h-732v-463h695v-160h-695v-496h732v-162h-894v247h-16q-66 -126 -201.5 -203.5t-290.5 -77.5q-301 0 -477 203.5t-176 552.5zM313 722q0 -275 130.5 -432.5t357.5 -157.5q217 0 342 157.5 t125 432.5q0 274 -124.5 431.5t-342.5 157.5q-226 0 -357 -158t-131 -431z" />
<glyph unicode="&#x153;" horiz-adv-x="1987" d="M108 539q0 259 133 408.5t364 149.5q135 0 241 -61.5t160 -170.5h16q54 109 157 170.5t232 61.5q218 0 343 -144.5t125 -396.5v-66h-775v-8q7 -160 90.5 -254t220.5 -94q210 0 278 145h172q-40 -140 -159.5 -219t-294.5 -79q-134 0 -241.5 62.5t-148.5 165.5h-16 q-42 -103 -151.5 -165.5t-248.5 -62.5q-231 0 -364 149.5t-133 408.5zM286 539q0 -192 83.5 -297.5t235.5 -105.5t235.5 105.5t83.5 297.5q0 193 -83.5 298t-235.5 105t-235.5 -105t-83.5 -298zM1104 629h594q-3 143 -82 230t-207 87t-211.5 -86.5t-93.5 -230.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1344" d="M70 1443h205l389 -641h16l389 641h205l-512 -822v-621h-180v621zM349 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5t-85.5 35.5t-35.5 85.5zM753 1696q0 50 35.5 85.5t85.5 35.5t85.5 -35.5t35.5 -85.5t-35.5 -85.5t-85.5 -35.5 t-85.5 35.5t-35.5 85.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1094" d="M164 1228l282 340h202l282 -340h-198l-177 227h-16l-177 -227h-198z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1093" d="M168 1263q0 136 61.5 213.5t170.5 77.5q44 0 82.5 -16t64.5 -39t48 -46.5t44.5 -39.5t44.5 -16q53 0 75.5 36t22.5 121h142q0 -136 -61.5 -213.5t-170.5 -77.5q-44 0 -82.5 16t-64.5 39.5t-48 46.5t-44.5 39t-44.5 16q-53 0 -75.5 -36t-22.5 -121h-142z" />
<glyph unicode="&#x2000;" horiz-adv-x="972" />
<glyph unicode="&#x2001;" horiz-adv-x="1945" />
<glyph unicode="&#x2002;" horiz-adv-x="972" />
<glyph unicode="&#x2003;" horiz-adv-x="1945" />
<glyph unicode="&#x2004;" horiz-adv-x="648" />
<glyph unicode="&#x2005;" horiz-adv-x="486" />
<glyph unicode="&#x2006;" horiz-adv-x="324" />
<glyph unicode="&#x2007;" horiz-adv-x="324" />
<glyph unicode="&#x2008;" horiz-adv-x="243" />
<glyph unicode="&#x2009;" horiz-adv-x="389" />
<glyph unicode="&#x200a;" horiz-adv-x="108" />
<glyph unicode="&#x2010;" horiz-adv-x="966" d="M155 499v163h656v-163h-656z" />
<glyph unicode="&#x2011;" horiz-adv-x="966" d="M155 499v163h656v-163h-656z" />
<glyph unicode="&#x2012;" horiz-adv-x="966" d="M155 499v163h656v-163h-656z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 499v163h1024v-163h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 499v163h2048v-163h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="605" d="M144 904l181 539h125l-111 -539h-195z" />
<glyph unicode="&#x2019;" horiz-adv-x="605" d="M180 904l91 539h195l-161 -539h-125z" />
<glyph unicode="&#x201a;" horiz-adv-x="605" d="M173 -367l95 569h191l-157 -569h-129z" />
<glyph unicode="&#x201c;" horiz-adv-x="1013" d="M146 874l177 569h129l-115 -569h-191zM549 874l157 569h129l-95 -569h-191z" />
<glyph unicode="&#x201d;" horiz-adv-x="1013" d="M178 874l95 569h191l-157 -569h-129zM561 874l115 569h191l-177 -569h-129z" />
<glyph unicode="&#x201e;" horiz-adv-x="1013" d="M178 -367l95 569h191l-157 -569h-129zM561 -367l115 569h191l-177 -569h-129z" />
<glyph unicode="&#x2022;" horiz-adv-x="1032" d="M212 559q0 128 88 216t216 88t216 -88t88 -216t-88 -216t-216 -88t-216 88t-88 216z" />
<glyph unicode="&#x2026;" horiz-adv-x="1642" d="M174 120q0 54 38.5 92t91.5 38q54 0 92 -38t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5zM692 120q0 54 38.5 92t91.5 38q54 0 92 -38t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5zM1198 120q0 54 38.5 92t91.5 38q54 0 92 -38 t38 -92q0 -53 -38 -91.5t-92 -38.5q-53 0 -91.5 38.5t-38.5 91.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="389" />
<glyph unicode="&#x2039;" horiz-adv-x="912" d="M179 561v8l350 416h204l-351 -412v-16l351 -412h-204z" />
<glyph unicode="&#x203a;" horiz-adv-x="912" d="M179 145l351 412v16l-351 412h204l350 -416v-8l-350 -416h-204z" />
<glyph unicode="&#x205f;" horiz-adv-x="486" />
<glyph unicode="&#x20ac;" d="M103 494v136h194q-4 39 -4 91q0 55 4 98h-194v136h217q42 167 144.5 284t240 172.5t301.5 55.5q135 0 201 -22v-163q-74 18 -201 18q-177 0 -307.5 -89t-185.5 -256h536v-136h-565q-5 -52 -5 -99q0 -32 4 -90h566v-136h-538q53 -171 184.5 -262t310.5 -91q140 0 201 18 v-162q-63 -21 -201 -21q-165 0 -303.5 56.5t-241 175t-142.5 286.5h-216z" />
<glyph unicode="&#x2122;" horiz-adv-x="1982" d="M43 1308v135h744v-135h-295v-765h-155v765h-294zM944 543v900h182l257 -665h10l256 665h182v-900h-144v639h-8l-236 -607h-110l-236 607h-9v-639h-144z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1080" d="M0 0v1080h1080v-1080h-1080z" />
<glyph unicode="&#xfb01;" horiz-adv-x="1245" d="M55 934v144h179v120q0 318 313 318q65 0 116 -10v-140q-26 5 -81 5q-92 0 -134 -44t-42 -134v-115h247v-144h-247v-934h-172v934h-179zM873 1417q0 49 35.5 84.5t84.5 35.5t84.5 -35.5t35.5 -84.5t-35.5 -84.5t-84.5 -35.5t-84.5 35.5t-35.5 84.5zM907 0v1078h172v-1078 h-172z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1257" d="M55 934v144h179v120q0 318 313 318q65 0 116 -10v-140q-26 5 -81 5q-92 0 -134 -44t-42 -134v-115h247v-144h-247v-934h-172v934h-179zM913 0v1506h172v-1506h-172z" />
<hkern u1="&#x23;" u2="&#x34;" k="30" />
<hkern u1="&#x26;" u2="&#x37;" k="30" />
<hkern u1="&#x26;" u2="&#x31;" k="30" />
<hkern u1="&#x2f;" u2="&#x34;" k="40" />
<hkern u1="&#x31;" u2="&#x26;" k="20" />
<hkern u1="&#x32;" u2="&#x40;" k="10" />
<hkern u1="&#x32;" u2="&#x34;" k="30" />
<hkern u1="&#x34;" u2="&#x31;" k="20" />
<hkern u1="&#x35;" u2="_" k="30" />
<hkern u1="&#x35;" u2="\" k="-30" />
<hkern u1="&#x36;" u2="_" k="90" />
<hkern u1="&#x36;" u2="\" k="-30" />
<hkern u1="&#x37;" u2="_" k="320" />
<hkern u1="&#x37;" u2="\" k="-40" />
<hkern u1="&#x37;" u2="&#x40;" k="40" />
<hkern u1="&#x37;" u2="&#x3c;" k="180" />
<hkern u1="&#x37;" u2="&#x39;" k="20" />
<hkern u1="&#x37;" u2="&#x38;" k="30" />
<hkern u1="&#x37;" u2="&#x37;" k="-40" />
<hkern u1="&#x37;" u2="&#x35;" k="20" />
<hkern u1="&#x37;" u2="&#x34;" k="120" />
<hkern u1="&#x37;" u2="&#x33;" k="30" />
<hkern u1="&#x37;" u2="&#x2f;" k="60" />
<hkern u1="&#x37;" u2="&#x26;" k="90" />
<hkern u1="&#x37;" u2="&#x23;" k="110" />
<hkern u1="&#x38;" u2="&#xc5;" k="20" />
<hkern u1="&#x38;" u2="&#xc4;" k="20" />
<hkern u1="&#x38;" u2="&#xc3;" k="20" />
<hkern u1="&#x38;" u2="&#xc2;" k="20" />
<hkern u1="&#x38;" u2="&#xc1;" k="20" />
<hkern u1="&#x38;" u2="&#xc0;" k="20" />
<hkern u1="&#x38;" u2="_" k="90" />
<hkern u1="&#x38;" u2="A" k="20" />
<hkern u1="&#x3e;" u2="&#x37;" k="150" />
<hkern u1="&#x3f;" u2="&#x34;" k="50" />
<hkern u1="&#x40;" u2="&#x37;" k="40" />
<hkern u1="&#x40;" u2="&#x32;" k="20" />
<hkern u1="\" u2="&#x37;" k="30" />
<hkern u1="\" u2="&#x31;" k="20" />
<hkern u1="_" u2="p" k="-90" />
<hkern u1="_" u2="&#x39;" k="90" />
<hkern u1="_" u2="&#x38;" k="90" />
<hkern u1="_" u2="&#x35;" k="90" />
<hkern u1="_" u2="&#x34;" k="110" />
<hkern u1="_" u2="&#x33;" k="90" />
<hkern u1="_" u2="&#x31;" k="220" />
<hkern u1="d" u2="&#xec;" k="-40" />
<hkern u1="l" u2="&#xec;" k="-40" />
<hkern u1="&#xb5;" u2="&#xac;" k="70" />
<hkern u1="&#xb5;" u2="_" k="60" />
<hkern u1="&#xb5;" u2="&#x3c;" k="80" />
<hkern u1="&#xb5;" u2="&#x34;" k="40" />
<hkern u1="&#xb5;" u2="&#x2f;" k="70" />
<hkern u1="&#xb5;" u2="&#x26;" k="20" />
<hkern u1="&#xb7;" u2="&#x26;" k="30" />
<hkern u1="&#xbf;" u2="&#x34;" k="60" />
<hkern u1="&#xfb02;" u2="&#xec;" k="-40" />
<hkern g1="periodcentered" 	g2="Eth" 	k="-40" />
<hkern g1="periodcentered" 	g2="T" 	k="160" />
<hkern g1="periodcentered" 	g2="V" 	k="80" />
<hkern g1="periodcentered" 	g2="W" 	k="60" />
<hkern g1="periodcentered" 	g2="X" 	k="90" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="100" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="periodcentered" 	g2="AE" 	k="100" />
<hkern g1="periodcentered" 	g2="J" 	k="90" />
<hkern g1="periodcentered" 	g2="Z" 	k="60" />
<hkern g1="periodcentered" 	g2="a,aacute,aring,ae" 	k="10" />
<hkern g1="periodcentered" 	g2="agrave,acircumflex,atilde,adieresis" 	k="10" />
<hkern g1="periodcentered" 	g2="x" 	k="30" />
<hkern g1="periodcentered" 	g2="z" 	k="30" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="110" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="110" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="200" />
<hkern g1="Thorn" 	g2="quotesinglbase,quotedblbase" 	k="250" />
<hkern g1="Thorn" 	g2="T" 	k="110" />
<hkern g1="Thorn" 	g2="V" 	k="100" />
<hkern g1="Thorn" 	g2="W" 	k="50" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="Thorn" 	g2="AE" 	k="130" />
<hkern g1="Thorn" 	g2="J" 	k="50" />
<hkern g1="Thorn" 	g2="Z" 	k="80" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="180" />
<hkern g1="ampersand" 	g2="uni00B2,uni00B3,uni00B9" 	k="90" />
<hkern g1="ampersand" 	g2="ordfeminine,registered,ordmasculine" 	k="80" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="50" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="60" />
<hkern g1="ampersand" 	g2="T" 	k="110" />
<hkern g1="ampersand" 	g2="V" 	k="130" />
<hkern g1="ampersand" 	g2="W" 	k="80" />
<hkern g1="ampersand" 	g2="v,y,yacute" 	k="60" />
<hkern g1="ampersand" 	g2="ydieresis" 	k="60" />
<hkern g1="ampersand" 	g2="w" 	k="60" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="40" />
<hkern g1="at" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="at" 	g2="V" 	k="30" />
<hkern g1="at" 	g2="v,y,yacute" 	k="30" />
<hkern g1="at" 	g2="ydieresis" 	k="30" />
<hkern g1="at" 	g2="X" 	k="20" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="290" />
<hkern g1="backslash" 	g2="comma,period,ellipsis" 	k="-80" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="230" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="30" />
<hkern g1="backslash" 	g2="T" 	k="80" />
<hkern g1="backslash" 	g2="V" 	k="100" />
<hkern g1="backslash" 	g2="W" 	k="100" />
<hkern g1="backslash" 	g2="v,y,yacute" 	k="100" />
<hkern g1="backslash" 	g2="ydieresis" 	k="100" />
<hkern g1="backslash" 	g2="w" 	k="60" />
<hkern g1="backslash" 	g2="X" 	k="20" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="40" />
<hkern g1="backslash" 	g2="j" 	k="-60" />
<hkern g1="braceright" 	g2="quoteleft,quotedblleft" 	k="70" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="130" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="exclamdown" 	g2="T" 	k="130" />
<hkern g1="exclamdown" 	g2="V" 	k="70" />
<hkern g1="exclamdown" 	g2="W" 	k="30" />
<hkern g1="five" 	g2="comma,period,ellipsis" 	k="70" />
<hkern g1="five" 	g2="ordfeminine,registered,ordmasculine" 	k="10" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="four" 	g2="comma,period,ellipsis" 	k="70" />
<hkern g1="four" 	g2="quotesinglbase,quotedblbase" 	k="30" />
<hkern g1="four" 	g2="uni00B2,uni00B3,uni00B9" 	k="30" />
<hkern g1="four" 	g2="asterisk,asciicircum,degree" 	k="20" />
<hkern g1="four" 	g2="ordfeminine,registered,ordmasculine" 	k="10" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="T" 	k="30" />
<hkern g1="four" 	g2="V" 	k="20" />
<hkern g1="four" 	g2="W" 	k="20" />
<hkern g1="four" 	g2="Z" 	k="10" />
<hkern g1="four" 	g2="v,y,yacute" 	k="30" />
<hkern g1="four" 	g2="ydieresis" 	k="30" />
<hkern g1="four" 	g2="w" 	k="30" />
<hkern g1="four" 	g2="x" 	k="10" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="140" />
<hkern g1="germandbls" 	g2="quotesinglbase,quotedblbase" 	k="100" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="50" />
<hkern g1="greater" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="greater" 	g2="quoteleft,quotedblleft" 	k="190" />
<hkern g1="greater" 	g2="T" 	k="160" />
<hkern g1="greater" 	g2="V" 	k="150" />
<hkern g1="greater" 	g2="W" 	k="150" />
<hkern g1="greater" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="greater" 	g2="Z" 	k="120" />
<hkern g1="greater" 	g2="v,y,yacute" 	k="60" />
<hkern g1="greater" 	g2="ydieresis" 	k="60" />
<hkern g1="greater" 	g2="w" 	k="60" />
<hkern g1="greater" 	g2="X" 	k="110" />
<hkern g1="greater" 	g2="x" 	k="60" />
<hkern g1="greater" 	g2="z" 	k="40" />
<hkern g1="mu" 	g2="quoteleft,quotedblleft" 	k="100" />
<hkern g1="mu" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="mu" 	g2="plus,hyphen,divide" 	k="80" />
<hkern g1="mu" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="mu" 	g2="uni00B2,uni00B3,uni00B9" 	k="-90" />
<hkern g1="mu" 	g2="quoteright,quotedblright" 	k="-60" />
<hkern g1="multiply" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="multiply" 	g2="T" 	k="110" />
<hkern g1="multiply" 	g2="V" 	k="80" />
<hkern g1="multiply" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="multiply" 	g2="v,y,yacute" 	k="40" />
<hkern g1="multiply" 	g2="ydieresis" 	k="40" />
<hkern g1="one" 	g2="quoteleft,quotedblleft" 	k="50" />
<hkern g1="one" 	g2="guillemotleft,guilsinglleft" 	k="50" />
<hkern g1="one" 	g2="comma,period,ellipsis" 	k="50" />
<hkern g1="one" 	g2="quotesinglbase,quotedblbase" 	k="90" />
<hkern g1="one" 	g2="quoteright,quotedblright" 	k="50" />
<hkern g1="one" 	g2="quotedbl,quotesingle" 	k="40" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="120" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="130" />
<hkern g1="question" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="120" />
<hkern g1="question" 	g2="AE" 	k="170" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="120" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="70" />
<hkern g1="seven" 	g2="guillemotleft,guilsinglleft" 	k="120" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="40" />
<hkern g1="seven" 	g2="zero,six" 	k="30" />
<hkern g1="seven" 	g2="comma,period,ellipsis" 	k="250" />
<hkern g1="seven" 	g2="quotesinglbase,quotedblbase" 	k="290" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="60" />
<hkern g1="seven" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="220" />
<hkern g1="seven" 	g2="J" 	k="160" />
<hkern g1="seven" 	g2="x" 	k="40" />
<hkern g1="seven" 	g2="a,aacute,aring,ae" 	k="90" />
<hkern g1="seven" 	g2="agrave,acircumflex,atilde,adieresis" 	k="90" />
<hkern g1="seven" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="100" />
<hkern g1="seven" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="100" />
<hkern g1="seven" 	g2="s" 	k="80" />
<hkern g1="seven" 	g2="u,uacute" 	k="50" />
<hkern g1="seven" 	g2="ugrave,ucircumflex,udieresis" 	k="50" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="six" 	g2="comma,period,ellipsis" 	k="70" />
<hkern g1="six" 	g2="quotesinglbase,quotedblbase" 	k="90" />
<hkern g1="six" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="six" 	g2="X" 	k="10" />
<hkern g1="slash" 	g2="comma,period,ellipsis" 	k="80" />
<hkern g1="slash" 	g2="T" 	k="-30" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="slash" 	g2="J" 	k="60" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="two" 	g2="guillemotleft,guilsinglleft" 	k="50" />
<hkern g1="two" 	g2="comma,period,ellipsis" 	k="30" />
<hkern g1="two" 	g2="ordfeminine,registered,ordmasculine" 	k="10" />
<hkern g1="two" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="10" />
<hkern g1="two" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="10" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="200" />
<hkern g1="underscore" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="underscore" 	g2="zero,six" 	k="90" />
<hkern g1="underscore" 	g2="asterisk,asciicircum,degree" 	k="140" />
<hkern g1="underscore" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="80" />
<hkern g1="underscore" 	g2="T" 	k="110" />
<hkern g1="underscore" 	g2="V" 	k="160" />
<hkern g1="underscore" 	g2="W" 	k="160" />
<hkern g1="underscore" 	g2="J" 	k="30" />
<hkern g1="underscore" 	g2="v,y,yacute" 	k="160" />
<hkern g1="underscore" 	g2="ydieresis" 	k="160" />
<hkern g1="underscore" 	g2="w" 	k="160" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="80" />
<hkern g1="underscore" 	g2="j" 	k="-190" />
<hkern g1="underscore" 	g2="a,aacute,aring,ae" 	k="40" />
<hkern g1="underscore" 	g2="agrave,acircumflex,atilde,adieresis" 	k="40" />
<hkern g1="underscore" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="30" />
<hkern g1="underscore" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="30" />
<hkern g1="underscore" 	g2="s" 	k="30" />
<hkern g1="underscore" 	g2="u,uacute" 	k="30" />
<hkern g1="underscore" 	g2="ugrave,ucircumflex,udieresis" 	k="30" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bullet" 	k="30" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="equal,plusminus" 	k="100" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotright,guilsinglright" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,divide" 	k="30" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="170" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="30" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="130" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="110" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,aacute,aring,ae" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="agrave,acircumflex,atilde,adieresis" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="30" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,uacute" 	k="50" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ugrave,ucircumflex,udieresis" 	k="50" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v,y,yacute" 	k="140" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ydieresis" 	k="140" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="140" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="uni00B2,uni00B3,uni00B9" 	k="180" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="270" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="200" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,asciicircum,degree" 	k="160" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="160" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ordfeminine,registered,ordmasculine" 	k="150" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="zero,six" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eight" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="five" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="less" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="logicalnot" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="multiply" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="120" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="30" />
<hkern g1="AE" 	g2="periodcentered" 	k="10" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="40" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="B" 	g2="ordfeminine,registered,ordmasculine" 	k="20" />
<hkern g1="B" 	g2="Eth" 	k="-60" />
<hkern g1="B" 	g2="AE" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="50" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="-30" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="40" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="30" />
<hkern g1="C,Ccedilla" 	g2="underscore" 	k="80" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="backslash" 	k="-40" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="periodcentered" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="four" 	k="20" />
<hkern g1="F" 	g2="equal,plusminus" 	k="30" />
<hkern g1="F" 	g2="a,aacute,aring,ae" 	k="70" />
<hkern g1="F" 	g2="agrave,acircumflex,atilde,adieresis" 	k="70" />
<hkern g1="F" 	g2="z" 	k="70" />
<hkern g1="F" 	g2="zero,six" 	k="30" />
<hkern g1="F" 	g2="backslash" 	k="-20" />
<hkern g1="F" 	g2="eight" 	k="30" />
<hkern g1="F" 	g2="periodcentered" 	k="10" />
<hkern g1="F" 	g2="AE" 	k="180" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="F" 	g2="underscore" 	k="140" />
<hkern g1="F" 	g2="four" 	k="30" />
<hkern g1="F" 	g2="J" 	k="100" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="170" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="240" />
<hkern g1="F" 	g2="ampersand" 	k="50" />
<hkern g1="F" 	g2="slash" 	k="100" />
<hkern g1="F" 	g2="three" 	k="20" />
<hkern g1="F" 	g2="two" 	k="20" />
<hkern g1="G" 	g2="T" 	k="30" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="G" 	g2="AE" 	k="50" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="G" 	g2="X" 	k="30" />
<hkern g1="G" 	g2="slash" 	k="30" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="backslash" 	k="-40" />
<hkern g1="K" 	g2="equal,plusminus" 	k="60" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="100" />
<hkern g1="K" 	g2="plus,hyphen,divide" 	k="220" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="80" />
<hkern g1="K" 	g2="a,aacute,aring,ae" 	k="20" />
<hkern g1="K" 	g2="agrave,acircumflex,atilde,adieresis" 	k="20" />
<hkern g1="K" 	g2="f,uniFB01,uniFB02" 	k="30" />
<hkern g1="K" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="80" />
<hkern g1="K" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="80" />
<hkern g1="K" 	g2="s" 	k="50" />
<hkern g1="K" 	g2="t" 	k="40" />
<hkern g1="K" 	g2="u,uacute" 	k="70" />
<hkern g1="K" 	g2="ugrave,ucircumflex,udieresis" 	k="70" />
<hkern g1="K" 	g2="v,y,yacute" 	k="130" />
<hkern g1="K" 	g2="ydieresis" 	k="130" />
<hkern g1="K" 	g2="w" 	k="130" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="50" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="K" 	g2="ordfeminine,registered,ordmasculine" 	k="60" />
<hkern g1="K" 	g2="zero,six" 	k="40" />
<hkern g1="K" 	g2="five" 	k="20" />
<hkern g1="K" 	g2="less" 	k="130" />
<hkern g1="K" 	g2="logicalnot" 	k="100" />
<hkern g1="K" 	g2="multiply" 	k="40" />
<hkern g1="K" 	g2="nine" 	k="30" />
<hkern g1="K" 	g2="one" 	k="20" />
<hkern g1="K" 	g2="periodcentered" 	k="90" />
<hkern g1="K" 	g2="four" 	k="50" />
<hkern g1="K" 	g2="ampersand" 	k="30" />
<hkern g1="K" 	g2="three" 	k="20" />
<hkern g1="K" 	g2="S" 	k="50" />
<hkern g1="K" 	g2="at" 	k="30" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="120" />
<hkern g1="L" 	g2="plus,hyphen,divide" 	k="170" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="60" />
<hkern g1="L" 	g2="T" 	k="190" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="50" />
<hkern g1="L" 	g2="V" 	k="140" />
<hkern g1="L" 	g2="W" 	k="140" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="190" />
<hkern g1="L" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="50" />
<hkern g1="L" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="50" />
<hkern g1="L" 	g2="t" 	k="50" />
<hkern g1="L" 	g2="v,y,yacute" 	k="140" />
<hkern g1="L" 	g2="ydieresis" 	k="140" />
<hkern g1="L" 	g2="w" 	k="130" />
<hkern g1="L" 	g2="uni00B2,uni00B3,uni00B9" 	k="310" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="170" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="140" />
<hkern g1="L" 	g2="asterisk,asciicircum,degree" 	k="270" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="180" />
<hkern g1="L" 	g2="ordfeminine,registered,ordmasculine" 	k="190" />
<hkern g1="L" 	g2="zero,six" 	k="30" />
<hkern g1="L" 	g2="backslash" 	k="40" />
<hkern g1="L" 	g2="eight" 	k="20" />
<hkern g1="L" 	g2="less" 	k="40" />
<hkern g1="L" 	g2="logicalnot" 	k="260" />
<hkern g1="L" 	g2="one" 	k="90" />
<hkern g1="L" 	g2="periodcentered" 	k="120" />
<hkern g1="L" 	g2="question" 	k="140" />
<hkern g1="L" 	g2="seven" 	k="30" />
<hkern g1="L" 	g2="four" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="30" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="60" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="50" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="60" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="ordfeminine,registered,ordmasculine" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="seven" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="60" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="50" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="80" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="90" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="80" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="80" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="60" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="40" />
<hkern g1="P" 	g2="a,aacute,aring,ae" 	k="40" />
<hkern g1="P" 	g2="agrave,acircumflex,atilde,adieresis" 	k="40" />
<hkern g1="P" 	g2="f,uniFB01,uniFB02" 	k="-50" />
<hkern g1="P" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="30" />
<hkern g1="P" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="30" />
<hkern g1="P" 	g2="s" 	k="30" />
<hkern g1="P" 	g2="t" 	k="-50" />
<hkern g1="P" 	g2="v,y,yacute" 	k="-40" />
<hkern g1="P" 	g2="ydieresis" 	k="-40" />
<hkern g1="P" 	g2="backslash" 	k="-30" />
<hkern g1="P" 	g2="periodcentered" 	k="10" />
<hkern g1="P" 	g2="seven" 	k="10" />
<hkern g1="P" 	g2="AE" 	k="230" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="90" />
<hkern g1="P" 	g2="X" 	k="60" />
<hkern g1="P" 	g2="underscore" 	k="170" />
<hkern g1="P" 	g2="four" 	k="60" />
<hkern g1="P" 	g2="J" 	k="110" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="200" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="290" />
<hkern g1="P" 	g2="ampersand" 	k="60" />
<hkern g1="P" 	g2="slash" 	k="100" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="Z" 	k="40" />
<hkern g1="R" 	g2="V" 	k="30" />
<hkern g1="R" 	g2="W" 	k="30" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="40" />
<hkern g1="R" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="R" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="30" />
<hkern g1="R" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="30" />
<hkern g1="R" 	g2="s" 	k="20" />
<hkern g1="R" 	g2="t" 	k="-50" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="R" 	g2="periodcentered" 	k="10" />
<hkern g1="R" 	g2="four" 	k="20" />
<hkern g1="S" 	g2="V" 	k="20" />
<hkern g1="S" 	g2="W" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="S" 	g2="X" 	k="10" />
<hkern g1="T" 	g2="bullet" 	k="80" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="260" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="100" />
<hkern g1="T" 	g2="plus,hyphen,divide" 	k="150" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="60" />
<hkern g1="T" 	g2="a,aacute,aring,ae" 	k="210" />
<hkern g1="T" 	g2="agrave,acircumflex,atilde,adieresis" 	k="150" />
<hkern g1="T" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="200" />
<hkern g1="T" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="160" />
<hkern g1="T" 	g2="s" 	k="150" />
<hkern g1="T" 	g2="u,uacute" 	k="150" />
<hkern g1="T" 	g2="ugrave,ucircumflex,udieresis" 	k="150" />
<hkern g1="T" 	g2="v,y,yacute" 	k="130" />
<hkern g1="T" 	g2="ydieresis" 	k="130" />
<hkern g1="T" 	g2="w" 	k="130" />
<hkern g1="T" 	g2="x" 	k="120" />
<hkern g1="T" 	g2="z" 	k="120" />
<hkern g1="T" 	g2="zero,six" 	k="60" />
<hkern g1="T" 	g2="backslash" 	k="-40" />
<hkern g1="T" 	g2="less" 	k="160" />
<hkern g1="T" 	g2="periodcentered" 	k="160" />
<hkern g1="T" 	g2="AE" 	k="250" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="170" />
<hkern g1="T" 	g2="underscore" 	k="110" />
<hkern g1="T" 	g2="four" 	k="140" />
<hkern g1="T" 	g2="J" 	k="150" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="170" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="240" />
<hkern g1="T" 	g2="ampersand" 	k="70" />
<hkern g1="T" 	g2="slash" 	k="160" />
<hkern g1="T" 	g2="three" 	k="20" />
<hkern g1="T" 	g2="i,iacute" 	k="20" />
<hkern g1="T" 	g2="igrave,icircumflex,idieresis" 	k="-60" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="150" />
<hkern g1="T" 	g2="colon,semicolon" 	k="80" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="backslash" 	k="-40" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="40" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="80" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="50" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="130" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="100" />
<hkern g1="V" 	g2="bullet" 	k="60" />
<hkern g1="V" 	g2="equal,plusminus" 	k="130" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="110" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="50" />
<hkern g1="V" 	g2="plus,hyphen,divide" 	k="100" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="50" />
<hkern g1="V" 	g2="a,aacute,aring,ae" 	k="100" />
<hkern g1="V" 	g2="agrave,acircumflex,atilde,adieresis" 	k="100" />
<hkern g1="V" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="100" />
<hkern g1="V" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="100" />
<hkern g1="V" 	g2="s" 	k="90" />
<hkern g1="V" 	g2="u,uacute" 	k="50" />
<hkern g1="V" 	g2="ugrave,ucircumflex,udieresis" 	k="50" />
<hkern g1="V" 	g2="v,y,yacute" 	k="30" />
<hkern g1="V" 	g2="ydieresis" 	k="30" />
<hkern g1="V" 	g2="w" 	k="30" />
<hkern g1="V" 	g2="x" 	k="30" />
<hkern g1="V" 	g2="z" 	k="30" />
<hkern g1="V" 	g2="zero,six" 	k="20" />
<hkern g1="V" 	g2="backslash" 	k="-40" />
<hkern g1="V" 	g2="eight" 	k="30" />
<hkern g1="V" 	g2="less" 	k="150" />
<hkern g1="V" 	g2="logicalnot" 	k="120" />
<hkern g1="V" 	g2="multiply" 	k="80" />
<hkern g1="V" 	g2="nine" 	k="10" />
<hkern g1="V" 	g2="periodcentered" 	k="80" />
<hkern g1="V" 	g2="AE" 	k="260" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="130" />
<hkern g1="V" 	g2="underscore" 	k="160" />
<hkern g1="V" 	g2="four" 	k="70" />
<hkern g1="V" 	g2="J" 	k="120" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="200" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="230" />
<hkern g1="V" 	g2="ampersand" 	k="100" />
<hkern g1="V" 	g2="slash" 	k="150" />
<hkern g1="V" 	g2="S" 	k="20" />
<hkern g1="V" 	g2="at" 	k="70" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="60" />
<hkern g1="V" 	g2="colon,semicolon" 	k="120" />
<hkern g1="W" 	g2="equal,plusminus" 	k="130" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="110" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="50" />
<hkern g1="W" 	g2="plus,hyphen,divide" 	k="100" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="60" />
<hkern g1="W" 	g2="a,aacute,aring,ae" 	k="100" />
<hkern g1="W" 	g2="agrave,acircumflex,atilde,adieresis" 	k="100" />
<hkern g1="W" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="100" />
<hkern g1="W" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="100" />
<hkern g1="W" 	g2="s" 	k="70" />
<hkern g1="W" 	g2="u,uacute" 	k="50" />
<hkern g1="W" 	g2="ugrave,ucircumflex,udieresis" 	k="50" />
<hkern g1="W" 	g2="v,y,yacute" 	k="30" />
<hkern g1="W" 	g2="ydieresis" 	k="30" />
<hkern g1="W" 	g2="w" 	k="30" />
<hkern g1="W" 	g2="x" 	k="30" />
<hkern g1="W" 	g2="z" 	k="30" />
<hkern g1="W" 	g2="zero,six" 	k="20" />
<hkern g1="W" 	g2="backslash" 	k="-40" />
<hkern g1="W" 	g2="eight" 	k="30" />
<hkern g1="W" 	g2="less" 	k="150" />
<hkern g1="W" 	g2="periodcentered" 	k="60" />
<hkern g1="W" 	g2="AE" 	k="180" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="110" />
<hkern g1="W" 	g2="underscore" 	k="160" />
<hkern g1="W" 	g2="four" 	k="70" />
<hkern g1="W" 	g2="J" 	k="120" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="200" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="220" />
<hkern g1="W" 	g2="ampersand" 	k="100" />
<hkern g1="W" 	g2="slash" 	k="140" />
<hkern g1="W" 	g2="three" 	k="30" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="60" />
<hkern g1="W" 	g2="colon,semicolon" 	k="100" />
<hkern g1="X" 	g2="bullet" 	k="60" />
<hkern g1="X" 	g2="equal,plusminus" 	k="100" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="30" />
<hkern g1="X" 	g2="plus,hyphen,divide" 	k="130" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="50" />
<hkern g1="X" 	g2="a,aacute,aring,ae" 	k="20" />
<hkern g1="X" 	g2="agrave,acircumflex,atilde,adieresis" 	k="20" />
<hkern g1="X" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="70" />
<hkern g1="X" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="70" />
<hkern g1="X" 	g2="s" 	k="30" />
<hkern g1="X" 	g2="t" 	k="40" />
<hkern g1="X" 	g2="u,uacute" 	k="30" />
<hkern g1="X" 	g2="ugrave,ucircumflex,udieresis" 	k="30" />
<hkern g1="X" 	g2="v,y,yacute" 	k="60" />
<hkern g1="X" 	g2="ydieresis" 	k="60" />
<hkern g1="X" 	g2="w" 	k="60" />
<hkern g1="X" 	g2="asterisk,asciicircum,degree" 	k="30" />
<hkern g1="X" 	g2="ordfeminine,registered,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="zero,six" 	k="60" />
<hkern g1="X" 	g2="backslash" 	k="-20" />
<hkern g1="X" 	g2="eight" 	k="10" />
<hkern g1="X" 	g2="less" 	k="110" />
<hkern g1="X" 	g2="periodcentered" 	k="90" />
<hkern g1="X" 	g2="four" 	k="30" />
<hkern g1="X" 	g2="J" 	k="40" />
<hkern g1="X" 	g2="ampersand" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="30" />
<hkern g1="X" 	g2="three" 	k="30" />
<hkern g1="X" 	g2="two" 	k="20" />
<hkern g1="X" 	g2="S" 	k="10" />
<hkern g1="X" 	g2="at" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bullet" 	k="80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="equal,plusminus" 	k="190" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="120" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,divide" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,aacute,aring,ae" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="agrave,acircumflex,atilde,adieresis" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="50" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,uacute" 	k="120" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ugrave,ucircumflex,udieresis" 	k="120" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ydieresis" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk,asciicircum,degree" 	k="30" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ordfeminine,registered,ordmasculine" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero,six" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-30" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="50" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="less" 	k="210" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="logicalnot" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="multiply" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="230" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="underscore" 	k="200" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="140" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="200" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="220" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="120" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="140" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="40" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="40" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="110" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="120" />
<hkern g1="Z" 	g2="bullet" 	k="30" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="Z" 	g2="plus,hyphen,divide" 	k="80" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="60" />
<hkern g1="Z" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="30" />
<hkern g1="Z" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="30" />
<hkern g1="Z" 	g2="zero,six" 	k="20" />
<hkern g1="Z" 	g2="less" 	k="120" />
<hkern g1="Z" 	g2="periodcentered" 	k="60" />
<hkern g1="Z" 	g2="four" 	k="50" />
<hkern g1="c,ccedilla" 	g2="T" 	k="130" />
<hkern g1="c,ccedilla" 	g2="V" 	k="60" />
<hkern g1="c,ccedilla" 	g2="W" 	k="60" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="c,ccedilla" 	g2="f,uniFB01,uniFB02" 	k="-40" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="c,ccedilla" 	g2="ordfeminine,registered,ordmasculine" 	k="20" />
<hkern g1="c,ccedilla" 	g2="seven" 	k="40" />
<hkern g1="c,ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="c,ccedilla" 	g2="X" 	k="30" />
<hkern g1="e,ae,egrave,oe" 	g2="T" 	k="200" />
<hkern g1="e,ae,egrave,oe" 	g2="V" 	k="100" />
<hkern g1="e,ae,egrave,oe" 	g2="W" 	k="100" />
<hkern g1="e,ae,egrave,oe" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="e,ae,egrave,oe" 	g2="v,y,yacute" 	k="20" />
<hkern g1="e,ae,egrave,oe" 	g2="ydieresis" 	k="20" />
<hkern g1="e,ae,egrave,oe" 	g2="x" 	k="40" />
<hkern g1="e,ae,egrave,oe" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="e,ae,egrave,oe" 	g2="ordfeminine,registered,ordmasculine" 	k="50" />
<hkern g1="e,ae,egrave,oe" 	g2="seven" 	k="40" />
<hkern g1="e,ae,egrave,oe" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="e,ae,egrave,oe" 	g2="X" 	k="50" />
<hkern g1="e,ae,egrave,oe" 	g2="underscore" 	k="30" />
<hkern g1="eacute,ecircumflex,edieresis" 	g2="T" 	k="160" />
<hkern g1="f" 	g2="bullet" 	k="30" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="f" 	g2="plus,hyphen,divide" 	k="50" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-30" />
<hkern g1="f" 	g2="a,aacute,aring,ae" 	k="30" />
<hkern g1="f" 	g2="agrave,acircumflex,atilde,adieresis" 	k="30" />
<hkern g1="f" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="45" />
<hkern g1="f" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="45" />
<hkern g1="f" 	g2="s" 	k="50" />
<hkern g1="f" 	g2="uni00B2,uni00B3,uni00B9" 	k="-50" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-40" />
<hkern g1="f" 	g2="periodcentered" 	k="20" />
<hkern g1="f" 	g2="AE" 	k="60" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="f" 	g2="underscore" 	k="30" />
<hkern g1="f" 	g2="four" 	k="120" />
<hkern g1="f" 	g2="J" 	k="120" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="110" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="180" />
<hkern g1="f" 	g2="ampersand" 	k="30" />
<hkern g1="f" 	g2="slash" 	k="50" />
<hkern g1="g,q" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="g,q" 	g2="seven" 	k="30" />
<hkern g1="g,q" 	g2="underscore" 	k="-70" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="T" 	k="150" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="30" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="V" 	k="100" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="W" 	k="100" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="v,y,yacute" 	k="40" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="ydieresis" 	k="40" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="w" 	k="50" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="quoteleft,quotedblleft" 	k="90" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="ordfeminine,registered,ordmasculine" 	k="20" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="one" 	k="80" />
<hkern g1="a,h,m,n,agrave,aring" 	g2="seven" 	k="30" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="T" 	k="150" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="30" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="V" 	k="100" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="W" 	k="100" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="v,y,yacute" 	k="40" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="w" 	k="50" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="quoteleft,quotedblleft" 	k="90" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="ordfeminine,registered,ordmasculine" 	k="20" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="one" 	k="80" />
<hkern g1="aacute,acircumflex,atilde,adieresis,ntilde" 	g2="seven" 	k="30" />
<hkern g1="i,j,igrave,uniFB01" 	g2="T" 	k="20" />
<hkern g1="i,j,igrave,uniFB01" 	g2="quoteleft,quotedblleft" 	k="50" />
<hkern g1="i,j,igrave,uniFB01" 	g2="quotedbl,quotesingle" 	k="30" />
<hkern g1="iacute,icircumflex,idieresis" 	g2="T" 	k="-60" />
<hkern g1="k" 	g2="bullet" 	k="60" />
<hkern g1="k" 	g2="equal,plusminus" 	k="20" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="100" />
<hkern g1="k" 	g2="plus,hyphen,divide" 	k="90" />
<hkern g1="k" 	g2="T" 	k="110" />
<hkern g1="k" 	g2="V" 	k="40" />
<hkern g1="k" 	g2="W" 	k="40" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="k" 	g2="a,aacute,aring,ae" 	k="30" />
<hkern g1="k" 	g2="agrave,acircumflex,atilde,adieresis" 	k="30" />
<hkern g1="k" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="50" />
<hkern g1="k" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="50" />
<hkern g1="k" 	g2="s" 	k="40" />
<hkern g1="k" 	g2="zero,six" 	k="50" />
<hkern g1="k" 	g2="less" 	k="170" />
<hkern g1="k" 	g2="logicalnot" 	k="140" />
<hkern g1="k" 	g2="periodcentered" 	k="40" />
<hkern g1="k" 	g2="seven" 	k="40" />
<hkern g1="k" 	g2="four" 	k="90" />
<hkern g1="k" 	g2="J" 	k="10" />
<hkern g1="k" 	g2="ampersand" 	k="20" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="T" 	k="200" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="V" 	k="100" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="W" 	k="100" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="v,y,yacute" 	k="40" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="ydieresis" 	k="40" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="w" 	k="40" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="x" 	k="50" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="z" 	k="30" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="ordfeminine,registered,ordmasculine" 	k="70" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="one" 	k="50" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="seven" 	k="60" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="AE" 	k="10" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="X" 	k="70" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="underscore" 	k="30" />
<hkern g1="b,o,p,ograve,oslash,thorn" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="T" 	k="160" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="V" 	k="100" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="W" 	k="100" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="v,y,yacute" 	k="40" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="w" 	k="40" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="x" 	k="50" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="z" 	k="30" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="ordfeminine,registered,ordmasculine" 	k="70" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="one" 	k="50" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="seven" 	k="60" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="AE" 	k="10" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="X" 	k="70" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="underscore" 	k="30" />
<hkern g1="oacute,ocircumflex,otilde" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="r" 	g2="bullet" 	k="20" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="r" 	g2="plus,hyphen,divide" 	k="80" />
<hkern g1="r" 	g2="T" 	k="110" />
<hkern g1="r" 	g2="V" 	k="10" />
<hkern g1="r" 	g2="W" 	k="10" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-60" />
<hkern g1="r" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="30" />
<hkern g1="r" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="30" />
<hkern g1="r" 	g2="s" 	k="20" />
<hkern g1="r" 	g2="t" 	k="-60" />
<hkern g1="r" 	g2="uni00B2,uni00B3,uni00B9" 	k="-90" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-60" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-130" />
<hkern g1="r" 	g2="less" 	k="80" />
<hkern g1="r" 	g2="logicalnot" 	k="70" />
<hkern g1="r" 	g2="question" 	k="-40" />
<hkern g1="r" 	g2="seven" 	k="20" />
<hkern g1="r" 	g2="AE" 	k="20" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="r" 	g2="X" 	k="20" />
<hkern g1="r" 	g2="underscore" 	k="60" />
<hkern g1="r" 	g2="four" 	k="40" />
<hkern g1="r" 	g2="J" 	k="110" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="130" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="r" 	g2="ampersand" 	k="20" />
<hkern g1="r" 	g2="slash" 	k="70" />
<hkern g1="r" 	g2="Z" 	k="60" />
<hkern g1="r" 	g2="m,n,p,r,ntilde" 	k="-20" />
<hkern g1="s" 	g2="T" 	k="150" />
<hkern g1="s" 	g2="V" 	k="90" />
<hkern g1="s" 	g2="W" 	k="70" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="s" 	g2="s" 	k="20" />
<hkern g1="s" 	g2="t" 	k="10" />
<hkern g1="s" 	g2="v,y,yacute" 	k="30" />
<hkern g1="s" 	g2="ydieresis" 	k="30" />
<hkern g1="s" 	g2="w" 	k="30" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="100" />
<hkern g1="s" 	g2="ordfeminine,registered,ordmasculine" 	k="30" />
<hkern g1="s" 	g2="one" 	k="30" />
<hkern g1="s" 	g2="seven" 	k="40" />
<hkern g1="s" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="s" 	g2="X" 	k="30" />
<hkern g1="s" 	g2="underscore" 	k="30" />
<hkern g1="t" 	g2="bullet" 	k="20" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="t" 	g2="plus,hyphen,divide" 	k="40" />
<hkern g1="t" 	g2="T" 	k="70" />
<hkern g1="t" 	g2="V" 	k="20" />
<hkern g1="t" 	g2="W" 	k="20" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="40" />
<hkern g1="t" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="15" />
<hkern g1="t" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="15" />
<hkern g1="t" 	g2="s" 	k="20" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="-40" />
<hkern g1="t" 	g2="less" 	k="50" />
<hkern g1="t" 	g2="periodcentered" 	k="10" />
<hkern g1="t" 	g2="four" 	k="40" />
<hkern g1="u,ugrave" 	g2="T" 	k="160" />
<hkern g1="u,ugrave" 	g2="V" 	k="40" />
<hkern g1="u,ugrave" 	g2="W" 	k="40" />
<hkern g1="u,ugrave" 	g2="Y,Yacute,Ydieresis" 	k="110" />
<hkern g1="u,ugrave" 	g2="f,uniFB01,uniFB02" 	k="-30" />
<hkern g1="u,ugrave" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="u,ugrave" 	g2="seven" 	k="20" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="T" 	k="160" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="V" 	k="40" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="W" 	k="40" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="110" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="f,uniFB01,uniFB02" 	k="-30" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="uacute,ucircumflex,udieresis" 	g2="seven" 	k="20" />
<hkern g1="v,y" 	g2="bullet" 	k="30" />
<hkern g1="v,y" 	g2="equal,plusminus" 	k="80" />
<hkern g1="v,y" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="v,y" 	g2="plus,hyphen,divide" 	k="20" />
<hkern g1="v,y" 	g2="T" 	k="130" />
<hkern g1="v,y" 	g2="V" 	k="30" />
<hkern g1="v,y" 	g2="W" 	k="30" />
<hkern g1="v,y" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="v,y" 	g2="a,aacute,aring,ae" 	k="40" />
<hkern g1="v,y" 	g2="agrave,acircumflex,atilde,adieresis" 	k="40" />
<hkern g1="v,y" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="40" />
<hkern g1="v,y" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="40" />
<hkern g1="v,y" 	g2="s" 	k="30" />
<hkern g1="v,y" 	g2="quoteleft,quotedblleft" 	k="30" />
<hkern g1="v,y" 	g2="zero,six" 	k="20" />
<hkern g1="v,y" 	g2="less" 	k="70" />
<hkern g1="v,y" 	g2="logicalnot" 	k="80" />
<hkern g1="v,y" 	g2="multiply" 	k="40" />
<hkern g1="v,y" 	g2="AE" 	k="140" />
<hkern g1="v,y" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="140" />
<hkern g1="v,y" 	g2="X" 	k="60" />
<hkern g1="v,y" 	g2="underscore" 	k="160" />
<hkern g1="v,y" 	g2="four" 	k="40" />
<hkern g1="v,y" 	g2="J" 	k="120" />
<hkern g1="v,y" 	g2="comma,period,ellipsis" 	k="150" />
<hkern g1="v,y" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="v,y" 	g2="ampersand" 	k="50" />
<hkern g1="v,y" 	g2="slash" 	k="110" />
<hkern g1="v,y" 	g2="Z" 	k="60" />
<hkern g1="v,y" 	g2="at" 	k="30" />
<hkern g1="yacute,ydieresis" 	g2="bullet" 	k="30" />
<hkern g1="yacute,ydieresis" 	g2="equal,plusminus" 	k="80" />
<hkern g1="yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="yacute,ydieresis" 	g2="plus,hyphen,divide" 	k="20" />
<hkern g1="yacute,ydieresis" 	g2="T" 	k="130" />
<hkern g1="yacute,ydieresis" 	g2="V" 	k="30" />
<hkern g1="yacute,ydieresis" 	g2="W" 	k="30" />
<hkern g1="yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="yacute,ydieresis" 	g2="a,aacute,aring,ae" 	k="40" />
<hkern g1="yacute,ydieresis" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="40" />
<hkern g1="yacute,ydieresis" 	g2="s" 	k="30" />
<hkern g1="yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="30" />
<hkern g1="yacute,ydieresis" 	g2="zero,six" 	k="20" />
<hkern g1="yacute,ydieresis" 	g2="less" 	k="70" />
<hkern g1="yacute,ydieresis" 	g2="logicalnot" 	k="80" />
<hkern g1="yacute,ydieresis" 	g2="multiply" 	k="40" />
<hkern g1="yacute,ydieresis" 	g2="AE" 	k="140" />
<hkern g1="yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="140" />
<hkern g1="yacute,ydieresis" 	g2="X" 	k="60" />
<hkern g1="yacute,ydieresis" 	g2="underscore" 	k="160" />
<hkern g1="yacute,ydieresis" 	g2="four" 	k="40" />
<hkern g1="yacute,ydieresis" 	g2="J" 	k="120" />
<hkern g1="yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="150" />
<hkern g1="yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="yacute,ydieresis" 	g2="ampersand" 	k="50" />
<hkern g1="yacute,ydieresis" 	g2="slash" 	k="110" />
<hkern g1="yacute,ydieresis" 	g2="Z" 	k="60" />
<hkern g1="yacute,ydieresis" 	g2="at" 	k="30" />
<hkern g1="w" 	g2="bullet" 	k="30" />
<hkern g1="w" 	g2="equal,plusminus" 	k="60" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="w" 	g2="plus,hyphen,divide" 	k="20" />
<hkern g1="w" 	g2="T" 	k="130" />
<hkern g1="w" 	g2="V" 	k="30" />
<hkern g1="w" 	g2="W" 	k="30" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="w" 	g2="a,aacute,aring,ae" 	k="30" />
<hkern g1="w" 	g2="agrave,acircumflex,atilde,adieresis" 	k="30" />
<hkern g1="w" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="40" />
<hkern g1="w" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="40" />
<hkern g1="w" 	g2="s" 	k="30" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="w" 	g2="zero,six" 	k="20" />
<hkern g1="w" 	g2="less" 	k="60" />
<hkern g1="w" 	g2="logicalnot" 	k="60" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="140" />
<hkern g1="w" 	g2="X" 	k="60" />
<hkern g1="w" 	g2="underscore" 	k="160" />
<hkern g1="w" 	g2="four" 	k="40" />
<hkern g1="w" 	g2="J" 	k="110" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="150" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="w" 	g2="ampersand" 	k="60" />
<hkern g1="w" 	g2="slash" 	k="60" />
<hkern g1="w" 	g2="Z" 	k="60" />
<hkern g1="x" 	g2="bullet" 	k="60" />
<hkern g1="x" 	g2="equal,plusminus" 	k="70" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="x" 	g2="plus,hyphen,divide" 	k="40" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="x" 	g2="T" 	k="120" />
<hkern g1="x" 	g2="V" 	k="30" />
<hkern g1="x" 	g2="W" 	k="30" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="x" 	g2="f,uniFB01,uniFB02" 	k="-40" />
<hkern g1="x" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="50" />
<hkern g1="x" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="50" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="40" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="less" 	k="60" />
<hkern g1="x" 	g2="periodcentered" 	k="30" />
<hkern g1="x" 	g2="seven" 	k="20" />
<hkern g1="x" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="x" 	g2="four" 	k="40" />
<hkern g1="x" 	g2="ampersand" 	k="10" />
<hkern g1="z" 	g2="bullet" 	k="60" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="z" 	g2="plus,hyphen,divide" 	k="120" />
<hkern g1="z" 	g2="T" 	k="120" />
<hkern g1="z" 	g2="V" 	k="30" />
<hkern g1="z" 	g2="W" 	k="30" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="z" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="30" />
<hkern g1="z" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="30" />
<hkern g1="z" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="z" 	g2="less" 	k="110" />
<hkern g1="z" 	g2="logicalnot" 	k="70" />
<hkern g1="z" 	g2="multiply" 	k="10" />
<hkern g1="z" 	g2="periodcentered" 	k="30" />
<hkern g1="z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="bullet" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="bullet" 	g2="T" 	k="80" />
<hkern g1="bullet" 	g2="V" 	k="60" />
<hkern g1="bullet" 	g2="X" 	k="60" />
<hkern g1="bullet" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="bullet" 	g2="Z" 	k="30" />
<hkern g1="bullet" 	g2="v,y,yacute" 	k="30" />
<hkern g1="bullet" 	g2="ydieresis" 	k="30" />
<hkern g1="bullet" 	g2="w" 	k="30" />
<hkern g1="bullet" 	g2="x" 	k="60" />
<hkern g1="bullet" 	g2="z" 	k="60" />
<hkern g1="equal,plusminus" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="100" />
<hkern g1="equal,plusminus" 	g2="V" 	k="130" />
<hkern g1="equal,plusminus" 	g2="X" 	k="100" />
<hkern g1="equal,plusminus" 	g2="Y,Yacute,Ydieresis" 	k="190" />
<hkern g1="equal,plusminus" 	g2="v,y,yacute" 	k="80" />
<hkern g1="equal,plusminus" 	g2="ydieresis" 	k="80" />
<hkern g1="equal,plusminus" 	g2="w" 	k="60" />
<hkern g1="equal,plusminus" 	g2="x" 	k="70" />
<hkern g1="equal,plusminus" 	g2="AE" 	k="100" />
<hkern g1="equal,plusminus" 	g2="W" 	k="130" />
<hkern g1="equal,plusminus" 	g2="quoteleft,quotedblleft" 	k="110" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="100" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="60" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="120" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="60" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="30" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="zero,six" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="50" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="seven" 	k="40" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="260" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="130" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="80" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="ydieresis" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="60" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="50" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="110" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteleft,quotedblleft" 	k="170" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="50" />
<hkern g1="guillemotright,guilsinglright" 	g2="seven" 	k="100" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="40" />
<hkern g1="guillemotright,guilsinglright" 	g2="eight" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="one" 	k="80" />
<hkern g1="guillemotright,guilsinglright" 	g2="three" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="two" 	k="100" />
<hkern g1="plus,hyphen,divide" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="plus,hyphen,divide" 	g2="T" 	k="150" />
<hkern g1="plus,hyphen,divide" 	g2="V" 	k="100" />
<hkern g1="plus,hyphen,divide" 	g2="X" 	k="130" />
<hkern g1="plus,hyphen,divide" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="plus,hyphen,divide" 	g2="Z" 	k="50" />
<hkern g1="plus,hyphen,divide" 	g2="v,y,yacute" 	k="20" />
<hkern g1="plus,hyphen,divide" 	g2="ydieresis" 	k="20" />
<hkern g1="plus,hyphen,divide" 	g2="w" 	k="20" />
<hkern g1="plus,hyphen,divide" 	g2="x" 	k="40" />
<hkern g1="plus,hyphen,divide" 	g2="z" 	k="20" />
<hkern g1="plus,hyphen,divide" 	g2="AE" 	k="70" />
<hkern g1="plus,hyphen,divide" 	g2="W" 	k="100" />
<hkern g1="plus,hyphen,divide" 	g2="quoteleft,quotedblleft" 	k="120" />
<hkern g1="plus,hyphen,divide" 	g2="J" 	k="30" />
<hkern g1="plus,hyphen,divide" 	g2="t" 	k="40" />
<hkern g1="plus,hyphen,divide" 	g2="f,uniFB01,uniFB02" 	k="40" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="180" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="AE" 	k="270" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="J" 	k="230" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="four" 	k="110" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="t" 	k="-70" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="comma,period,ellipsis" 	k="220" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="quotesinglbase,quotedblbase" 	k="290" />
<hkern g1="uni00B2,uni00B3,uni00B9" 	g2="ampersand" 	k="30" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j" 	k="-60" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="20" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="170" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="200" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="200" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute" 	k="150" />
<hkern g1="comma,period,ellipsis" 	g2="ydieresis" 	k="150" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="150" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="200" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="390" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="120" />
<hkern g1="comma,period,ellipsis" 	g2="zero,six" 	k="80" />
<hkern g1="comma,period,ellipsis" 	g2="four" 	k="60" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="40" />
<hkern g1="comma,period,ellipsis" 	g2="eight" 	k="70" />
<hkern g1="comma,period,ellipsis" 	g2="one" 	k="70" />
<hkern g1="comma,period,ellipsis" 	g2="three" 	k="60" />
<hkern g1="comma,period,ellipsis" 	g2="two" 	k="40" />
<hkern g1="comma,period,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="60" />
<hkern g1="comma,period,ellipsis" 	g2="ampersand" 	k="70" />
<hkern g1="comma,period,ellipsis" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="40" />
<hkern g1="comma,period,ellipsis" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="40" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="90" />
<hkern g1="comma,period,ellipsis" 	g2="uni00B2,uni00B3,uni00B9" 	k="220" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="270" />
<hkern g1="comma,period,ellipsis" 	g2="asterisk,asciicircum,degree" 	k="280" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="190" />
<hkern g1="comma,period,ellipsis" 	g2="backslash" 	k="80" />
<hkern g1="comma,period,ellipsis" 	g2="five" 	k="65" />
<hkern g1="comma,period,ellipsis" 	g2="nine" 	k="80" />
<hkern g1="comma,period,ellipsis" 	g2="slash" 	k="-80" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="200" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="180" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="220" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute" 	k="100" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="ydieresis" 	k="100" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="50" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="120" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="250" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="one" 	k="130" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="uni00B2,uni00B3,uni00B9" 	k="190" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="200" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="200" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute" 	k="40" />
<hkern g1="quoteleft,quotedblleft" 	g2="ydieresis" 	k="40" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="50" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="190" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="210" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="80" />
<hkern g1="quoteleft,quotedblleft" 	g2="zero,six" 	k="70" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="130" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="30" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="250" />
<hkern g1="quoteleft,quotedblleft" 	g2="quotesinglbase,quotedblbase" 	k="250" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="120" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="140" />
<hkern g1="quoteleft,quotedblleft" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="140" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="190" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,aacute,aring,ae" 	k="60" />
<hkern g1="quoteleft,quotedblleft" 	g2="agrave,acircumflex,atilde,adieresis" 	k="60" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="60" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="80" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,uacute" 	k="60" />
<hkern g1="quoteleft,quotedblleft" 	g2="ugrave,ucircumflex,udieresis" 	k="60" />
<hkern g1="quoteleft,quotedblleft" 	g2="numbersign" 	k="30" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="130" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="270" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="ydieresis" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="40" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="40" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="250" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="250" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="40" />
<hkern g1="quoteright,quotedblright" 	g2="zero,six" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="200" />
<hkern g1="quoteright,quotedblright" 	g2="eight" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="170" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="390" />
<hkern g1="quoteright,quotedblright" 	g2="quotesinglbase,quotedblbase" 	k="380" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="130" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="130" />
<hkern g1="quoteright,quotedblright" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="130" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,asciicircum,degree" 	k="40" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="290" />
<hkern g1="quoteright,quotedblright" 	g2="a,aacute,aring,ae" 	k="110" />
<hkern g1="quoteright,quotedblright" 	g2="agrave,acircumflex,atilde,adieresis" 	k="110" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="130" />
<hkern g1="quoteright,quotedblright" 	g2="u,uacute" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="ugrave,ucircumflex,udieresis" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="numbersign" 	k="250" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="140" />
<hkern g1="quoteright,quotedblright" 	g2="equal,plusminus" 	k="110" />
<hkern g1="quoteright,quotedblright" 	g2="plus,hyphen,divide" 	k="120" />
<hkern g1="quoteright,quotedblright" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="110" />
<hkern g1="quoteright,quotedblright" 	g2="asciitilde" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="at" 	k="110" />
<hkern g1="quoteright,quotedblright" 	g2="braceleft" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="60" />
<hkern g1="quoteright,quotedblright" 	g2="less" 	k="190" />
<hkern g1="quoteright,quotedblright" 	g2="mu" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="underscore" 	k="80" />
<hkern g1="asterisk,asciicircum,degree" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="160" />
<hkern g1="asterisk,asciicircum,degree" 	g2="X" 	k="30" />
<hkern g1="asterisk,asciicircum,degree" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="asterisk,asciicircum,degree" 	g2="AE" 	k="220" />
<hkern g1="asterisk,asciicircum,degree" 	g2="J" 	k="200" />
<hkern g1="asterisk,asciicircum,degree" 	g2="zero,six" 	k="20" />
<hkern g1="asterisk,asciicircum,degree" 	g2="four" 	k="90" />
<hkern g1="asterisk,asciicircum,degree" 	g2="comma,period,ellipsis" 	k="280" />
<hkern g1="asterisk,asciicircum,degree" 	g2="ampersand" 	k="70" />
<hkern g1="asterisk,asciicircum,degree" 	g2="underscore" 	k="140" />
<hkern g1="asterisk,asciicircum,degree" 	g2="Eth" 	k="-70" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="160" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="250" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="250" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="30" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="130" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="30" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="190" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="70" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="150" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="X" 	k="20" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="AE" 	k="180" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="J" 	k="150" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="ampersand" 	k="80" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="c,d,e,g,o,q,ccedilla,eacute,eth,oacute,oslash,oe" 	k="70" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="egrave,ecircumflex,edieresis,ograve,ocircumflex,otilde,odieresis" 	k="70" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="a,aacute,aring,ae" 	k="50" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="agrave,acircumflex,atilde,adieresis" 	k="50" />
<hkern g1="ordfeminine,registered,ordmasculine,trademark" 	g2="s" 	k="30" />
<hkern g1="colon,semicolon" 	g2="T" 	k="80" />
<hkern g1="colon,semicolon" 	g2="V" 	k="120" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="120" />
<hkern g1="colon,semicolon" 	g2="W" 	k="100" />
<hkern g1="colon,semicolon" 	g2="quoteleft,quotedblleft" 	k="90" />
<hkern g1="colon,semicolon" 	g2="four" 	k="30" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="30" />
<hkern g1="colon,semicolon" 	g2="three" 	k="10" />
<hkern g1="colon,semicolon" 	g2="two" 	k="-10" />
<hkern g1="three,eight" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="three,eight" 	g2="V" 	k="30" />
<hkern g1="three,eight" 	g2="X" 	k="10" />
<hkern g1="three,eight" 	g2="Y,Yacute,Ydieresis" 	k="50" />
<hkern g1="three,eight" 	g2="W" 	k="30" />
<hkern g1="three,eight" 	g2="quoteleft,quotedblleft" 	k="60" />
<hkern g1="three,eight" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="three,eight" 	g2="comma,period,ellipsis" 	k="70" />
<hkern g1="three,eight" 	g2="quotesinglbase,quotedblbase" 	k="100" />
<hkern g1="three,eight" 	g2="quoteright,quotedblright" 	k="60" />
<hkern g1="three,eight" 	g2="quotedbl,quotesingle" 	k="30" />
<hkern g1="three,eight" 	g2="backslash" 	k="-30" />
<hkern g1="three,eight" 	g2="underscore" 	k="30" />
<hkern g1="zero,nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="zero,nine" 	g2="T" 	k="60" />
<hkern g1="zero,nine" 	g2="V" 	k="20" />
<hkern g1="zero,nine" 	g2="X" 	k="60" />
<hkern g1="zero,nine" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="zero,nine" 	g2="Z" 	k="20" />
<hkern g1="zero,nine" 	g2="v,y,yacute" 	k="20" />
<hkern g1="zero,nine" 	g2="ydieresis" 	k="20" />
<hkern g1="zero,nine" 	g2="w" 	k="20" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="W" 	k="20" />
<hkern g1="zero,nine" 	g2="quoteleft,quotedblleft" 	k="50" />
<hkern g1="zero,nine" 	g2="seven" 	k="40" />
<hkern g1="zero,nine" 	g2="comma,period,ellipsis" 	k="80" />
<hkern g1="zero,nine" 	g2="quotesinglbase,quotedblbase" 	k="100" />
<hkern g1="zero,nine" 	g2="quoteright,quotedblright" 	k="30" />
<hkern g1="zero,nine" 	g2="asterisk,asciicircum,degree" 	k="20" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="30" />
<hkern g1="zero,nine" 	g2="backslash" 	k="-60" />
<hkern g1="zero,nine" 	g2="underscore" 	k="90" />
</font>
</defs></svg> 