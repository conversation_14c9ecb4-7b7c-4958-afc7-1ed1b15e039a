<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>Maintenance Page</title>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .center-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: left;
      }

      .player-img {
        margin-bottom: 20px;
      }

      h1 {
        color: #fff;
        font-size: 61.47px;
        font-family: "Veneer Clean Soft";
        line-height: 86.05px;
        font-weight: 400;
      }

      @media (max-width: 1820px) {
        h1 {
          font-size: 55px;
        }
      }
      @media (max-width: 1600px) {
        h1 {
          font-size: 45px;
        }
      }
      @media (max-width: 1360px) {
        h1 {
          font-size: 40px;
        }
      }
      @media (max-width: 490px) {
        h1 {
          font-size: 31.36px;
        }
      }

      .players-box {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        
      }

        @media (max-width: 2500px) {
          .players-box {
          max-width: 1920px;
          margin: 0 auto;
          padding: 0px 10px;
        }}
        @media (max-width: 1910px) {
          .players-box {
          max-width: 1920px;
          /* max-width: 1280px; */
          margin: 0 auto;
          padding: 0px 10px;
          max-width: 1350px;
          margin: 0 auto;
          padding: 0px 10px;
        }}

        @media (max-width: 1290px) {
          .players-box{
            display: block;
            text-align: center;
          }
        }

        .players-img{
          /* display: none; */
          /* max-width: 70%; */
          max-width: 80%;
          /* max-width: 75%; */
          height: 100vh;
          object-fit: cover;
        }

        /* @media (max-width: 1156px) {
          .players-img{
            display: block;
              position: absolute;
              width: 490px;
              max-width: unset;
              height:unset;
              bottom: 0;
              left: 0;
              left: 30%;
              left: 18%;
          }
        } */
        @media (max-width: 1290px) {
          .players-img{
              position: absolute;
              width: 440px;
              max-width: unset;
              height:unset;
              bottom: 0;
              left: 0;
              left: 30%;
          }
        }
        @media (max-width: 860px) {
          .players-img{
            /* left: 20%; */
          }
        }
        @media (max-width: 690px) {
          .players-img{
            position: absolute;
            bottom: 0;
            left: 0%;
          }
        }
        @media (max-width: 620px) {
          .players-img{
            position: absolute;
            /* width: 380px; */
            width: 462px;
            bottom: 0;
            /* left: 10%; */
            left: 2%;
          }
        }
        @media (max-width: 490px) {
          .players-img{
            position: absolute;
            /* width: 360px; */
            bottom: 0;
            left: 10%;

            
          }
        }
        /* @media (max-width: 440px) {
          .players-img{
            position: absolute;
            width: 325px;
            bottom: 0;
            left: 2%;
          }
        } */
        @media (max-width: 490px) {
          .players-img{
            position: absolute;
            /* width: 320px; */
            /* width: 380px; */
            bottom: 0;
            /* left: 0 ; */
            width: 440px;
            left: -10%;
          }
        }
        @media (max-width: 400px) {
          .players-img{
            /* position: absolute; */
            /* width: 320px; */
            /* width: 380px; */
            /* bottom: 0; */
            /* left: 0 ; */
            /* width: 440px; */
   left: -16%;
          }

           @media (max-width: 390px) {
            .players-img{
              left: -26%;
            }
              
            }
            /* @media (max-width: 370px) {
              .players-img{
                left: -35% !important;
              }
              
            } */
        }

      p {
        font-weight: 300;
        color: #fff;
        font-size: 22.4px;
        font-family: "inter";
        line-height: 31.36px;
      }

      @media (max-width: 490px) {
        p {
          font-size: 16px;
        }
      }
      @media (max-width: 354px) {
        p {
          font-size: 14px;
        }
      }

      @media (max-width: 1120px) {
        .txt-box{
           margin-top: 5;
            text-align: center;
            width: 100%;
        }
      }
      @media (max-width: 490px) {
        .txt-box{
          /* margin-top: 50px; */
          margin-top: 18px;
     }
      }

      .maintenance-banner-box {
        background: linear-gradient(
          5deg,
          rgba(4, 17, 29, 1) 0%,
          rgba(2, 29, 51, 1) 59%
        );
        background-repeat: repeat;
        background-position: top center;
        background-size: auto;
        overflow: hidden;
        height: 100vh;
      }

      .maintenance-banner-box .image-container {
        background-image: url("https://i.postimg.cc/MKs4TF9y/maintenance-bg2.png");
        /* background-image: url("https://i.postimg.cc/6pfpT0Qs/Component-89-1-1.png"); */
         background-repeat: no-repeat;
          background-position: top center;
          background-size: cover;
          overflow: hidden;
        height: 100vh;
      }

      @media (max-width: 1156px) {
        .maintenance-banner-box .image-container{
          /* background-image: url("https://i.postimg.cc/J7PRZnNg/Group-42192.png"); */
          background-image: url("https://i.postimg.cc/MKs4TF9y/maintenance-bg2.png");
        }
      }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box {
        left: -48%;
        top: 40%;
      }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .title {
        font-family: "Veneer Clean Soft" !important;
        line-height: 86.05px !important;
        font-size: 61.47px !important;
        color: #fff !important;
      }

      @media (max-width: 1360px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .title {
          font-size: 34px;
        }
      }

      @media (max-width: 1120px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .title {
          font-size: 31.36px;
        }
              
            }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .des {
        margin-top: 23px !important;
        font-family: "inter" !important;
        font-size: 22.4px !important;
        color: #fff !important;
      }

      @media (max-width: 1120px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .des {
          font-size: 16px;
        }
              
            }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {
        height: 100vh;
        max-width: 70%;
        max-height: 100%;
      }

      @media (max-width: 1580px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {margin-left: -10%}
            
          }
          @media (max-width: 1440px) {
            .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {margin-left: -10%}
          }
          @media (max-width: 1120px) {
             .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {
          object-fit: cover;
            width: 500px;
            height: 700px;
            margin-top: -12%;
        }
            
          }

      @media (max-width: 1120px) {
        .body
          .maintenance-banner-box
          .image-container
          .content-container
          .players-box
          .players-box {
          flex-direction: column;
        }
      }
      
        .body
        .maintenance-banner-box
        .image-container
        .responsive-content-container {
        display: none;
      }

      .go-to-home-btn {
    margin-top: 18px;
    border: 1px solid #fff;
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
    line-height: 16px;
    font-family: "inter";
    font-weight: 500;
    padding: 10px 20px;
    background-color: transparent;
    text-transform: uppercase;
    cursor: pointer;
  }

  @media (max-width: 354px) {
    .go-to-home-btn {
      margin-top: 14px;
    }
  }

  .text{
    text-align: left;
    /* display: flex; */
    /* justify-content: flex-start; */
    /* flex-direction: column; */
    /* align-items: center; */
    /* margin-top: 100px; */
    position: absolute;
    top: 35%;
    left: 8%;
  }

  @media (max-width: 1156px) {

    .text{
text-align: unset;
    position: unset;
    }
    
  }
    </style>
  </head>

  <body>
    <script>
      if (performance.navigation.type === 1) {
          const prevPage = localStorage.getItem("previous_page");
        if (prevPage) {
          setTimeout(() => {
            window.location.href = prevPage;
            localStorage.removeItem("previous_page");
          }, 1000);
        } else{
          window.location.href = "/"
        }
       
      }
  </script>
    <script>
      function handleRefresh() {
        const prevPage = localStorage.getItem("previous_page");
        if (prevPage) {
          setTimeout(() => {
            window.location.href = prevPage;
            localStorage.removeItem("previous_page");
          }, 1000);
        } else {
          window.location.href = "/"
        }
      }
  </script>
  <!-- class="text" -->
    <div class="maintenance-banner-box">
      <div class="image-container">
        <div class="content-container">
          <div class="players-box">
            <div
             class="txt-box"
             
             >
              <h1 >We'll be back soon.</p>
              <p >We're busy updating the SmartB experience.<br/> Please check back soon for the exciting new updates.</p>
              <Button
                variant="outlined"
                class="go-to-home-btn"
                onclick="handleRefresh()"
              >
                Refresh
              </Button>
            </div>
            <img
              class="players-img"
              src="https://i.postimg.cc/nhvVZsxR/players.png"
            />
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
